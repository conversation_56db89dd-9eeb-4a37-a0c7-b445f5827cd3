import { Service } from 'typedi';
import { Repository } from 'typeorm';
import { AppDataSource } from '@app/orm/dataSource';
import { UOM } from '@app/orm/entities/UOM/UOM';
import { ICurrentUser } from '@app/interfaces';
import { HttpError } from 'routing-controllers';
import { Logger } from '@app/loaders/logger';
import { standardizeActiveUOM, standardizeConversionFactor, UOM_DEFAULTS } from '@app/utils/helpers/uom-helper';

export interface UOMValidationResult {
  isValid: boolean;
  uom?: UOM;
  error?: string;
}

export interface ActiveUOMValidationOptions {
  allowNull?: boolean;
  checkCustomer?: boolean;
  checkEnabled?: boolean;
  validateConversionFactor?: boolean;
}

@Service()
export class UOMValidationService {
  private uomRepository: Repository<UOM>;

  constructor() {
    this.uomRepository = AppDataSource.getRepository(UOM);
  }

  /**
   * Validate active_uom field with comprehensive checks
   * @param activeUom - The active_uom value to validate
   * @param user - Current user for customer validation
   * @param conversionFactor - Optional conversion factor to validate
   * @param options - Validation options
   * @returns UOMValidationResult
   */
  async validateActiveUOM(
    activeUom: string | null | undefined,
    user: ICurrentUser,
    conversionFactor?: number,
    options: ActiveUOMValidationOptions = {}
  ): Promise<UOMValidationResult> {
    const {
      allowNull = true,
      checkCustomer = true,
      checkEnabled = true,
      validateConversionFactor = true
    } = options;

    try {
      // Handle null/undefined cases
      if (!activeUom || activeUom.trim() === '') {
        if (allowNull) {
          return { isValid: true };
        }
        return {
          isValid: false,
          error: 'Active UOM is required'
        };
      }

      // Check if UOM exists
      const uom = await this.uomRepository.findOne({
        where: { name: activeUom.trim() }
      });

      if (!uom) {
        Logger.warn(`UOM validation failed: UOM '${activeUom}' not found`, {
          activeUom,
          userId: user.user_id,
          customerId: user.customer_id
        });
        return {
          isValid: false,
          error: `UOM '${activeUom}' does not exist`
        };
      }

      // Check if UOM is deleted
      if (uom.is_deleted === 1) {
        Logger.warn(`UOM validation failed: UOM '${activeUom}' is deleted`, {
          activeUom,
          userId: user.user_id,
          customerId: user.customer_id
        });
        return {
          isValid: false,
          error: `UOM '${activeUom}' is no longer available`
        };
      }

      // Check customer ownership (multi-tenancy)
      if (checkCustomer && uom.iot_customer && uom.iot_customer !== user.customer_id) {
        Logger.warn(`UOM validation failed: UOM '${activeUom}' belongs to different customer`, {
          activeUom,
          uomCustomer: uom.iot_customer,
          userCustomer: user.customer_id,
          userId: user.user_id
        });
        return {
          isValid: false,
          error: `UOM '${activeUom}' is not accessible`
        };
      }

      // Check if UOM is enabled
      if (checkEnabled && uom.enabled === 0) {
        Logger.warn(`UOM validation failed: UOM '${activeUom}' is disabled`, {
          activeUom,
          userId: user.user_id,
          customerId: user.customer_id
        });
        return {
          isValid: false,
          error: `UOM '${activeUom}' is currently disabled`
        };
      }

      // Validate conversion factor if provided
      if (validateConversionFactor && conversionFactor !== undefined) {
        const conversionValidation = this.validateConversionFactor(conversionFactor);
        if (!conversionValidation.isValid) {
          return conversionValidation;
        }
      }

      Logger.debug(`UOM validation successful for '${activeUom}'`, {
        activeUom,
        uomName: uom.uom_name,
        userId: user.user_id,
        customerId: user.customer_id
      });

      return {
        isValid: true,
        uom
      };

    } catch (error: any) {
      Logger.error('Error during UOM validation', {
        activeUom,
        userId: user.user_id,
        customerId: user.customer_id,
        error: error.message
      });

      return {
        isValid: false,
        error: 'Internal error during UOM validation'
      };
    }
  }

  /**
   * Validate conversion factor
   * @param conversionFactor - The conversion factor to validate
   * @returns UOMValidationResult
   */
  validateConversionFactor(conversionFactor: number): UOMValidationResult {
    if (conversionFactor === null || conversionFactor === undefined) {
      return {
        isValid: false,
        error: 'Conversion factor is required'
      };
    }

    if (typeof conversionFactor !== 'number' || isNaN(conversionFactor)) {
      return {
        isValid: false,
        error: 'Conversion factor must be a valid number'
      };
    }

    if (conversionFactor <= 0) {
      return {
        isValid: false,
        error: 'Conversion factor must be greater than 0'
      };
    }

    if (!isFinite(conversionFactor)) {
      return {
        isValid: false,
        error: 'Conversion factor must be a finite number'
      };
    }

    if (conversionFactor < UOM_DEFAULTS.MIN_CONVERSION_FACTOR || conversionFactor > UOM_DEFAULTS.MAX_CONVERSION_FACTOR) {
      return {
        isValid: false,
        error: `Conversion factor must be between ${UOM_DEFAULTS.MIN_CONVERSION_FACTOR} and ${UOM_DEFAULTS.MAX_CONVERSION_FACTOR}`
      };
    }

    return { isValid: true };
  }

  /**
   * Validate and throw error if invalid
   * @param activeUom - The active_uom value to validate
   * @param user - Current user for customer validation
   * @param conversionFactor - Optional conversion factor to validate
   * @param options - Validation options
   * @throws HttpError if validation fails
   * @returns UOM entity if valid
   */
  async validateActiveUOMOrThrow(
    activeUom: string | null | undefined,
    user: ICurrentUser,
    conversionFactor?: number,
    options: ActiveUOMValidationOptions = {}
  ): Promise<UOM | null> {
    const result = await this.validateActiveUOM(activeUom, user, conversionFactor, options);

    if (!result.isValid) {
      throw new HttpError(400, result.error || 'Invalid active UOM');
    }

    return result.uom || null;
  }

  /**
   * Get default conversion factor for UOM
   * @param uom - UOM entity
   * @returns Default conversion factor (always 1 for now)
   */
  getDefaultConversionFactor(uom?: UOM): number {
    // For now, default is always 1
    // In the future, this could be configurable per UOM
    return UOM_DEFAULTS.CONVERSION_FACTOR;
  }

  /**
   * Sanitize active_uom value
   * @param activeUom - Raw active_uom value
   * @returns Sanitized value or null
   */
  sanitizeActiveUOM(activeUom: string | null | undefined): string | null {
    return standardizeActiveUOM(activeUom);
  }

  /**
   * Sanitize conversion factor value
   * @param conversionFactor - Raw conversion factor value
   * @returns Sanitized value or default (1)
   */
  sanitizeConversionFactor(conversionFactor: number | null | undefined): number {
    return standardizeConversionFactor(conversionFactor);
  }
}
