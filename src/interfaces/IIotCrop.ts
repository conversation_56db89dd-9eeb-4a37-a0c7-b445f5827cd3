
/**
* Generate by D:\WORK\PYROJECT\VIIS\iot-backend-typescript\tools\gen_type.js
*/
export class IIotCrop {
	name!: string | number;
	plant_id?: string; // Link
	plant_label?: string; // Read Only
	zone_id?: string; // Link
	zone?: string; // Read Only
	image?: string; // Attach Image
	label?: string; // Data
	description?: string; // Small Text
	square?: string; // Data
	start_date?: string; // Date
	end_date?: string; // Date
	quantity_estimate?: number; // Float
	status?: "Done" | "In progress"; // Done|In progress
	avatar?: string; // Attach Image
	is_deleted?: string; // Check
	document_links?: string; // Data
	location?: string; // Geolocation
	images?: string; // Data
	tag?: string; // Link to tag
}
