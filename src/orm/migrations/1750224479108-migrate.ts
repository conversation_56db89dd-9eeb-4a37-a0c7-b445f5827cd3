import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrate1750224479108 implements MigrationInterface {
    name = 'Migrate1750224479108'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tabiot_tag" ADD "deleted" TIMESTAMP WITH TIME ZONE`);
        await queryRunner.query(`ALTER TABLE "tabiot_tag" ADD "crop_id" character varying(140)`);
        await queryRunner.query(`ALTER TABLE "tabiot_crop" ADD "tag" character varying(140)`);
        await queryRunner.query(`ALTER TABLE "tabiot_tag" ALTER COLUMN "creation" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "tabiot_tag" ALTER COLUMN "modified" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "tabiot_crop" ADD CONSTRAINT "FK_1ef4cf3edc47ef31155fa95d463" FOREIGN KEY ("tag") REFERENCES "tabiot_tag"("name") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tabiot_crop" DROP CONSTRAINT "FK_1ef4cf3edc47ef31155fa95d463"`);
        await queryRunner.query(`ALTER TABLE "tabiot_tag" ALTER COLUMN "modified" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "tabiot_tag" ALTER COLUMN "creation" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "tabiot_crop" DROP COLUMN "tag"`);
        await queryRunner.query(`ALTER TABLE "tabiot_tag" DROP COLUMN "crop_id"`);
        await queryRunner.query(`ALTER TABLE "tabiot_tag" DROP COLUMN "deleted"`);
    }

}
