// File: /home/<USER>/VIIS/iot-backend-typescript/src/orm/entities/farmingPlan/FarmingPlanTask.ts

import { Entity, Column, PrimaryColumn, ManyToOne, JoinColumn, OneToMany, OneToOne, ManyToMany, JoinTable } from 'typeorm';
import { CustomBaseEntity } from '../base/Base';
import { FarmingPlanState } from './FarmingPlanState';
import { IotCustomerUser } from '../customer/customer_user';
import { VietplantsFarmingPlanTask } from '../vietplants/VietplantsFarmingPlanTask';
import { FarmingPlanTaskTemplate, TaskType } from './template/FarmingPlanTaskTemplate';
import { Department } from '../organization/Department';
import { FarmingPlanEnvironmentTemplate } from './template/FarmingPlanEnvironmentTemplate';
import { TaskItemTransfer } from './taskItem/TaskItemTransfer';

//enum task status
export enum TaskStatus {
  PENDING = 'Pending',
  IN_PROGRESS = 'In progress',
  DONE = 'Done',
  PLAN = 'Plan',
  //các status cho phần quản lý môi trường vietplants
  //đang sử dụng
  IN_USE = 'InUse',
  //đang chờ sử dụng
  WAITING = 'Waiting',
  //đã dừng
  STOPPED = 'Stopped'
}


@Entity({ name: 'tabiot_farming_plan_task' })
export class FarmingPlanTask extends CustomBaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 140 })
  name: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  farming_plan_state?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  state_name?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  crop_name?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  label?: string;

  @Column({ type: 'text', nullable: true })
  image?: string;

  @Column({ type: 'timestamp', nullable: true })
  start_date?: Date;

  @Column({ type: 'timestamp', nullable: true })
  end_date?: Date;

  @Column({ type: 'varchar', length: 140, nullable: true })
  index?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  assigned_to?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  status?: TaskStatus;

  @Column({ type: 'varchar', length: 140, nullable: true })
  priority_level?: 'Normal' | 'Priority' | 'Important';

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'smallint', default: 0 })
  enable_origin_tracing: number;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  task_progress: number;

  @Column({ type: 'smallint', default: 0 })
  added_in_diary: number;

  /**
   * Flag to track whether stock entries have been processed for this task
   * Used to prevent duplicate transactions when executing confirm workflow multiple times
   */
  @Column({ type: 'boolean', default: false })
  stock_entries_processed: boolean;

  //involved_in_users
  @Column({ type: 'text', nullable: true })
  involved_in_users?: string;

  //tag
  @Column({ type: 'varchar', length: 140, nullable: true })
  tag?: string;

  //text_state
  @Column({ type: 'varchar', length: 140, nullable: true })
  text_state?: string;

  //text_plan
  @Column({ type: 'varchar', length: 140, nullable: true })
  text_plan?: string;

  //text_assign_user
  @Column({ type: 'varchar', length: 140, nullable: true })
  text_assign_user?: string;

  @Column({ type: 'smallint', default: 0 })
  is_template: number;

  @Column({ type: 'varchar', length: 140, nullable: true })
  template_id?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  department_id?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  environment_template_id?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  task_type?: TaskType;

  /**
   * @deprecated Use incomingTransfers and outgoingTransfers instead.
   * Kept for backward compatibility with existing data.
   */
  @Column({ type: 'varchar', length: 140, nullable: true })
  previous_task_id?: string;

  /**
   * @deprecated Use incomingTransfers and outgoingTransfers instead.
   * Kept for backward compatibility with existing data.
   */
  @Column({ type: 'text', nullable: true })
  task_chain_ids?: string; // Stored as JSON string of task IDs in the chain

  // Relations
  @ManyToOne(() => FarmingPlanState)
  @JoinColumn({ name: 'farming_plan_state' })
  farmingPlanState?: FarmingPlanState;

  @ManyToOne(() => IotCustomerUser)
  @JoinColumn({ name: 'assigned_to' })
  assignedUser?: IotCustomerUser;

  @ManyToOne(() => FarmingPlanTaskTemplate)
  @JoinColumn({ name: 'template_id' })
  template?: FarmingPlanTaskTemplate;

  // One-to-one relation with VietplantsFarmingPlanTask extension
  @OneToOne(() => VietplantsFarmingPlanTask, vietplantsTask => vietplantsTask.farmingPlanTask)
  vietplantsExtension?: VietplantsFarmingPlanTask;

  //many to one Department
  @ManyToOne(() => Department)
  @JoinColumn({ name: 'department_id' })
  department?: Department;

  @ManyToOne(() => FarmingPlanEnvironmentTemplate, { nullable: true })
  @JoinColumn({ name: 'environment_template_id' })
  environmentTemplate?: FarmingPlanEnvironmentTemplate;

  /**
   * Incoming transfers where this task is the target, representing parent tasks that contribute items to this task
   */
  @OneToMany(() => TaskItemTransfer, transfer => transfer.targetTask)
  incomingTransfers?: TaskItemTransfer[];

  /**
   * Outgoing transfers where this task is the source, representing child tasks that receive items from this task
   */
  @OneToMany(() => TaskItemTransfer, transfer => transfer.sourceTask)
  outgoingTransfers?: TaskItemTransfer[];
}