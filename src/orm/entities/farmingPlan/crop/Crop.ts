// File: /home/<USER>/VIIS/iot-backend-typescript/src/orm/entities/crop/Crop.ts

import { Entity, Column, PrimaryColumn, ManyToOne, JoinColumn, OneToMany, OneToOne } from 'typeorm';
import { CustomBaseEntity } from '../../base/Base';
import { TabiotZone } from '../../IoTZone/IoTZone';
import { VietplantsCrop } from '../../vietplants/VietplantsCrop';
import { FarmingPlan } from '../FarmingPlan';
import { Plant } from '../plant/Plant';
import { IoTTag } from '../tag/Tag';

@Entity({ name: 'tabiot_crop' })
export class Crop extends CustomBaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 140 })
  name: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  farming_plan?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  label?: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  zone?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  plant_id?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  plant?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  zone_id?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  square?: string;

  @Column({ type: 'date', nullable: true })
  start_date?: Date;

  @Column({ type: 'date', nullable: true })
  end_date?: Date;

  @Column({ type: 'text', nullable: true })
  image?: string;

  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  quantity_estimate: number;

  //quantity_actual
  @Column({ type: 'numeric', precision: 21, scale: 9, default: 0 })
  quantity_actual: number;

  @Column({ type: 'varchar', length: 140, nullable: true })
  status?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  plant_label?: string;

  @Column({ type: 'text', nullable: true })
  avatar?: string;

  //location
  @Column({ type: 'text', nullable: true })
  location?: string;

  //images
  @Column({ type: 'varchar', length: 140, nullable: true })
  images?: string;

  //document_links
  @Column({ type: 'varchar', length: 140, nullable: true })
  document_links?: string;

  @Column({ type: 'smallint', default: 0 })
  is_template: number;

  // Relations
  @ManyToOne(() => Plant)
  @JoinColumn({ name: 'plant_id' })
  plantRelation?: Plant;

  @ManyToOne(() => TabiotZone, zone => zone.crops, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'zone_id', referencedColumnName: 'name' })
  zoneRelation?: TabiotZone;

  @OneToMany(() => FarmingPlan, farmingPlan => farmingPlan.cropRelation)
  farmingPlans?: FarmingPlan[];

  // One-to-one relation with VietplantsCrop extension
  @OneToOne(() => VietplantsCrop, vietplantsCrop => vietplantsCrop.crop)
  vietplantsExtension?: VietplantsCrop;

  //is_deleted
  @Column({ type: 'smallint', default: 0 })
  is_deleted: number;

  //tag column
  @Column({ type: 'varchar', length: 140, nullable: true })
  tag?: string;

  //Many to one with Tag
  @ManyToOne(() => IoTTag, tag => tag.crops, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'tag', referencedColumnName: 'name' })
  tagRelation?: IoTTag;

}