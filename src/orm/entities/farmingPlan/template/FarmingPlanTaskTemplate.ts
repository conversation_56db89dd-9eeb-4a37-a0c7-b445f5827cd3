// File: /home/<USER>/VIIS/iot-backend-typescript/src/orm/entities/farmingPlan/FarmingPlanTaskTemplate.ts

import { Entity, Column, PrimaryColumn, ManyToOne, JoinColumn, OneToMany, ManyToMany, JoinTable } from 'typeorm';
import { CustomBaseEntity } from '../../base/Base';
import { FarmingPlanStateTemplate } from './FarmingPlanStateTemplate';
import { TabiotCustomer } from '../../customer/customer';
import { FarmingPlanTask } from '../FarmingPlanTask';
import { ItemTaskTemplate } from './ItemTaskTemplate';
import { FarmingPlanEnvironmentTemplate } from './FarmingPlanEnvironmentTemplate';
import { ProductionTaskTemplate } from './ProductionTaskTemplate';

//enum task type hiện tại chỉ dùng cho vietplants
export enum TaskType {
  ENV_PREPARATION = 'EnvPreparation',
  SUBCULTURE = 'Subculture',
  QUALITY_INSPECTION = 'QualityInspection',
  FINAL_QUALITY_INSPECTION = 'FinalQualityInspection',
  OTHER = 'Other',
  // task type cho phần quản lý môi trường
  // lô stock
  ENV_STOCK = 'EnvStock',
  // nồi hấp
  ENV_STEAM_POT = 'EnvSteamPot',
  // chiết rót
  ENV_POUR = 'EnvPour'
}


@Entity({ name: 'tabiot_farming_plan_task_template' })
export class FarmingPlanTaskTemplate extends CustomBaseEntity {
  @PrimaryColumn({ type: 'varchar', length: 140 })
  name: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  label?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  status?: 'Pending' | 'In progress' | 'Done' | 'Plan'

  @Column({ type: 'varchar', length: 140, nullable: true })
  priority_level?: 'Normal' | 'Priority' | 'Important';

  @Column({ type: 'text', nullable: true })
  image?: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  task_type?: TaskType;

  @Column({ type: 'varchar', length: 140, nullable: true })
  customer_id?: string;

  @Column({ type: 'varchar', length: 140, nullable: true })
  environment_template_id?: string;

  // Relations
  @ManyToOne(() => TabiotCustomer)
  @JoinColumn({ name: 'customer_id' })
  customer?: TabiotCustomer;

  @OneToMany(() => FarmingPlanTask, task => task.template)
  farmingPlanTasks?: FarmingPlanTask[];

  /**
   * List of item-task templates (detail records for each item in this task template).
   */
  @OneToMany(() => ItemTaskTemplate, itemTaskTemplate => itemTaskTemplate.taskTemplate)
  itemTaskTemplates!: ItemTaskTemplate[];

  /**
   * List of production-task templates (detail records for each item in this task template).
   */
  @OneToMany(() => ProductionTaskTemplate, productionTaskTemplate => productionTaskTemplate.taskTemplate)
  productionTaskTemplates!: ProductionTaskTemplate[];

  /**
   * Many-to-many relation with FarmingPlanStateTemplate for multi-select support.
   * This is the inverse side (owned by FarmingPlanStateTemplate).
   */
  @ManyToMany(() => FarmingPlanStateTemplate, stateTemplate => stateTemplate.tasks)
  farmingPlanStateTemplates!: FarmingPlanStateTemplate[];

  @ManyToOne(() => FarmingPlanEnvironmentTemplate, { nullable: true })
  @JoinColumn({ name: 'environment_template_id' })
  environmentTemplate?: FarmingPlanEnvironmentTemplate;
}