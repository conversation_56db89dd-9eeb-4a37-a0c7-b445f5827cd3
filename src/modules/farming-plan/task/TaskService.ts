import { FrappeService } from '@app/services/frappe/FrappeService';
import Container, { Service } from 'typedi';
import { ICurrentUser } from '@app/interfaces';
import jwt from 'jsonwebtoken';
import configs from '@app/configs';
import axios, { AxiosInstance, CreateAxiosDefaults } from 'axios';
import { HttpError } from 'routing-controllers';
import bodyParser from 'body-parser';
import { UserType } from '@app/utils/enums/usertype';
import { IIotProject } from '@app/interfaces/IIotProject';
import {
  ERPExecute,
  ERPExecuteTransaction,
  ERPExecuteWithTransaction,
  MyERPExecuteTransaction,
  MyERPExecuteTransactionErrorHandler,
} from '@app/loaders/pglib/PGDB';
import { IIotZone } from '@app/interfaces/IIotZone';
import { IIotDevice } from '@app/interfaces/IIotDevice';
import { IIotProductionFunction } from '@app/interfaces/IIotProductionFunction';
import { IIotCrop } from '@app/interfaces/IIotCrop';
import { IGeneralDoc } from '@app/interfaces/IGeneralDoc';
import { convertObjectArray, filterByIotCategoryId, filterByProductId } from './TaskValidator';
import { verify } from 'crypto';
import { IIotCropNote } from '@app/interfaces/IIotCropNote';
import { IIotFarmingPlan } from '@app/interfaces/IIotFarmingPlan';
import { IIotFarmingPlanTask } from '@app/interfaces/IIotFarmingPlanTask';
import { OtherService } from '@app/services/frappe/OtherService';
import { isValidAction } from 'morphism/dist/types/helpers';
import {
  areAllArraysEqual,
  generateDeleteSQLTaskArray,
  generateInsertSQLTaskArray,
  generateUpsertSQLTaskArray,
  generateUpdateOrInsertQueries,
  generateUpdateSQLTaskArray,
  getCropFromTaskName,
} from '@app/utils/helpers/helper';
import { FarmingPlanTask, FarmingPlanTaskArray } from '@app/interfaces/ITaskAllResource';
import { sortByLabel, sortByProperty } from './TaskHelper';
import { SqlQuery } from '@app/interfaces/ISQLQuery';
import { cropParticipantsVerify } from '@app/modules/crop-manage/crop/CropSQLHelper';
import moment from 'moment';
import { NotificationService } from '@app/modules/notification/NotificationService';
import { EmployeeInCropService } from '@app/modules/crop-manage/employee_in_crop/EmployeeInCropService';
import { IIotEmployeeInCrop } from '@app/interfaces/IIotEmployeeInCrop';
import { TaskItemService } from './task-item/TaskItemService';
import { TaskProductionService } from './task-production/TaskProductionService';

@Service()
export class TaskService {
  constructor(
    private frappeService: FrappeService,
    private otherService: OtherService,
    private notificationService: NotificationService,
    private employeeInCropService: EmployeeInCropService,
    private taskItemService: TaskItemService,
    private taskProductionService: TaskProductionService,
  ) {
    this.frappeService = Container.get(FrappeService);
    this.otherService = Container.get(OtherService);
    this.notificationService = Container.get(NotificationService);
    this.employeeInCropService = Container.get(EmployeeInCropService);
    this.taskItemService = Container.get(TaskItemService);
    this.taskProductionService = Container.get(TaskProductionService);
  }
  /**
   * CRUD CRUD iot_farming_plan_task
   */

  async getTask(user: ICurrentUser, params: any) {
    try {
      if (user.user_type === UserType.SYSTEM_USER) {
        const taskList: any = await this.frappeService.callSScript(
          'general-doc-list',
          {
            filters: JSON.stringify(params.filters) || JSON.stringify([]),
            or_filters: JSON.stringify(params.or_filters) || JSON.stringify([]),
            page: params.page || 1,
            size: params.size || 100,
            fields: JSON.stringify(params.fields) || JSON.stringify(['*']),
            order_by: params.order_by || null,
            group_by: params.group_by || null,
            doc_name: 'iot_farming_plan_task',
          },
          'GET',
        );
        return taskList;
      } else {
        /**
         * verify user
         */

        const cropList = await this.otherService.getCropList(user);
        const crops = cropList.map(item => `'${item.name}'`).join(', ');
        const verifyTask = await ERPExecute(
          `
                    SELECT task.name FROM "tabiot_farming_plan_task" as task
                    INNER JOIN
                        "tabiot_farming_plan_state" as state
                    ON
                        task.farming_plan_state = state.name
                    INNER JOIN
                        "tabiot_farming_plan" as plan
                    ON
                        state.farming_plan = plan.name
                    WHERE plan.crop IN (${crops})
                    `,
          [],
        );
        if (verifyTask.length === 0) {
          return {
            data: [],
            pagination: {
              pageNumber: 1,
              pageSize: 100,
              totalElements: 0,
              totalPages: 0,
            },
          };
        }
        // Join the array of strings into a comma-separated string
        let conditionArr: any[] = [];
        verifyTask.forEach((d: any) => {
          conditionArr.push(d.name);
        });
        // const filters: any = [['iot_farming_plan_task', 'name', 'in', conditionArr]]
        const filters: any = [];
        if (params.filters) {
          const paramsFilterArr: any[] = JSON.parse(params.filters);
          paramsFilterArr.forEach((d: any) => {
            filters.push(d);
          });
        }
        console.log('filters', filters);
        let taskList: any = await this.frappeService.callSScript(
          'general-doc-list',
          {
            filters: JSON.stringify(filters) || JSON.stringify([]),
            or_filters: JSON.stringify(params.or_filters) || JSON.stringify([]),
            page: params.page || 1,
            size: params.size || 100,
            fields: JSON.stringify(params.fields) || JSON.stringify(['*']),
            order_by: params.order_by || null,
            group_by: params.group_by || null,
            doc_name: 'iot_farming_plan_task',
          },
          'GET',
        );
        return taskList;
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Upsert tasks and resources array - supports both insert and update operations
   * Uses PostgreSQL's INSERT ... ON CONFLICT DO UPDATE for proper upsert functionality
   */
  async upsertTaskandResourceArray(user: ICurrentUser, body: FarmingPlanTaskArray, added_in_diary: number = 0) {
    try {
      const taskArr: FarmingPlanTask[] = body.tasks;
      console.log("taskArr for upsert", taskArr);

      if (!taskArr.length) {
        return [];
      }

      // Validate field consistency
      let keywords: any[] = [];
      taskArr.map((taskObj: any) => {
        const keys = Object.keys(taskObj);
        let taskKeys = keys.filter(
          (item: any) =>
            item !== 'involve_in_users' &&
            item !== 'todo_list' &&
            item !== 'item_list' &&
            item !== 'worksheet_list' &&
            item !== 'prod_quantity_list',
        );
        keywords.push(taskKeys);
      });

      const taskOnlyArr = taskArr.map((taskObj: any) => {
        const { involve_in_users, todo_list, item_list, worksheet_list, prod_quantity_list, ...rest } = taskObj;
        return rest;
      });
      console.log('taskOnlyArr for upsert', taskOnlyArr);

      // const checkField = areAllArraysEqual(keywords);
      // if (!checkField) {
      //   throw new HttpError(400, 'Các task object có field không giống nhau, vui lòng kiểm tra lại');
      // }

      // Generate upsert SQL queries
      console.log("start to upsert tasks");
      const sqlUpsertTaskArray = generateUpsertSQLTaskArray(taskOnlyArr, taskArr as any, added_in_diary);
      console.log("end to upsert tasks");

      // Execute upsert queries in transaction
      const sqlQueries = sqlUpsertTaskArray.map(sql => ({ query: sql, params: [] }));
      const query = await ERPExecuteWithTransaction(sqlQueries);

      /**
       * ADDITIONAL LOGIC - same as create method
       */

      // UPDATE field total_qty_in_crop for all task
      const iotCategoryList = filterByIotCategoryId(query);
      if (iotCategoryList.length > 0) {
        for (const item of iotCategoryList) {
          await this.taskItemService.updateTotalItemQtyInCrop(user, {
            task_id: item.task_id,
            item_id: item.iot_category_id,
          });
        }
      }

      const productList = filterByProductId(query);
      if (productList.length > 0) {
        for (const item of productList) {
          await this.taskProductionService.updateTotalProductionQtyInCrop(user, {
            task_id: item.task_id,
            item_id: item.product_id,
          });
        }
      }

      // Handle employee_in_crop for assign_user
      const assignUserList: any = query.filter((obj: any) => {
        const condition = obj.customer_user !== undefined && obj.task !== undefined;
        return condition;
      });
      console.log('assignUserList', assignUserList);
      if (assignUserList.length > 0) {
        for (const assignUser of assignUserList) {
          const crop = await getCropFromTaskName(assignUser.task);
          const employeeInCrop: IIotEmployeeInCrop = {
            iot_crop: crop,
            iot_customer_user: assignUser.customer_user,
            name: `${crop}-${assignUser.customer_user}`,
          };
          await this.employeeInCropService.upsertEmployeeInCrop(user, employeeInCrop);
        }
      }

      // Create notification for assigned user in each task
      // Filter for task records only (those with farming_plan_state field)
      const taskRecords = query.filter((record: any) =>
        record.farming_plan_state !== undefined && record.farming_plan_state !== null
      );

      // Process notifications for each task
      for (const element of taskRecords) {
        const assign_user = element.assigned_to;

        if (assign_user) {
          // Insert on duplicate update employee_in_crop
          const crop = await getCropFromTaskName(element.name ? element.name : '');
          const employeeInCrop: IIotEmployeeInCrop = {
            iot_crop: crop,
            iot_customer_user: assign_user,
            name: `${crop}-${assign_user}`,
          };
          await this.employeeInCropService.upsertEmployeeInCrop(user, employeeInCrop);

          // Check if notification already exists
          const existNoti = await ERPExecute(
            `
            SELECT name
            FROM tabiot_notification
            WHERE entity = '${element.name}'
              AND message = 'Bạn vừa được giao công việc: ${element.label}'
            `,
            [],
          );

          if (existNoti.length === 0) {
            // Create notification for user
            const notiObject = {
              customer_user: assign_user,
              message: `Bạn vừa được giao công việc: ${element.label}`,
              created_at: moment().add(7, 'hours').format('YYYY-MM-DD HH:mm:ss'),
              entity: element.name,
              type: 'task',
              is_read: 0,
            };
            const noti: any = await this.frappeService.generalCreate({
              doc_name: 'iot_notification',
              data: notiObject,
            });

            // Push notification to mobile
            const pushNotiBody = {
              topic: `arn:aws:sns:ap-southeast-1:434827713262:${assign_user}`,
              title: 'Thông báo công việc',
              body: `Bạn vừa được giao công việc: ${element.label}`,
              noti_id: noti.data.name,
              entity: element.name ? element.name : '',
              type: 'Alarm',
            };
            await this.notificationService.publishMobileNotificationToTopicAdmin(pushNotiBody);
          }
        }
      }
      //return flatten query
      return query.flat();
    } catch (error) {
      throw new HttpError(400, 'Something went wrong when handling upsert array task: ' + error);
    }
  }

  async createTaskandResourceArray(user: ICurrentUser, body: FarmingPlanTaskArray, added_in_diary: number = 0) {
    try {
      const taskArr: FarmingPlanTask[] = body.tasks;
      console.log("taskArr", taskArr)
      if (!taskArr.length) {
        return [];
      }
      let keywords: any[] = [];
      taskArr.map((taskObj: any) => {
        const keys = Object.keys(taskObj);
        let taskKeys = keys.filter(
          (item: any) =>
            item !== 'involve_in_users' &&
            item !== 'todo_list' &&
            item !== 'item_list' &&
            item !== 'worksheet_list' &&
            item !== 'prod_quantity_list',
        );
        keywords.push(taskKeys);
      });

      const taskOnlyArr = taskArr.map((taskObj: any) => {
        const { involve_in_users, todo_list, item_list, worksheet_list, prod_quantity_list, ...rest } = taskObj;
        return rest;
      });
      console.log('taskOnlyArr', taskOnlyArr);

      const checkField = areAllArraysEqual(keywords);
      if (!checkField) {
        throw new HttpError(400, 'Các task object có field không giống nhau, vui lòng kiểm tra lại');
      }
      //SQL create task
      console.log("start to create tasks")
      const sqlCreateTaskArray = generateInsertSQLTaskArray(taskOnlyArr, taskArr as any, added_in_diary);
      console.log("end to create tasks")
      const query = await ERPExecuteTransaction(sqlCreateTaskArray, []);

      /**
       * ADDITIONAL LOGIC
       */

      //UPDATE field total_qty_in_crop for all task
      const iotCategoryList = filterByIotCategoryId(query);
      //update total_qty_in_crop for all tabiot_warehouse_item_task_used with the given iot_category_id
      if (iotCategoryList.length > 0) {
        for (const item of iotCategoryList) {
          await this.taskItemService.updateTotalItemQtyInCrop(user, {
            task_id: item.task_id,
            item_id: item.iot_category_id,
          });
        }
      }

      const productList = filterByProductId(query);
      //update total_qty_in_crop for all tabiot_production_quantity with the given product_id
      if (productList.length > 0) {
        for (const item of productList) {
          await this.taskProductionService.updateTotalProductionQtyInCrop(user, {
            task_id: item.task_id,
            item_id: item.product_id,
          });
        }
      }

      //insert on duplicate update employee_in_crop for table iot_assign_user
      const assignUserList: any = query.filter((obj: any) => {
        const condition = obj.customer_user !== undefined && obj.task !== undefined;
        return condition;
      });
      console.log('assignUserList', assignUserList);
      if (assignUserList.length > 0) {
        for (const assignUser of assignUserList) {
          const crop = await getCropFromTaskName(assignUser.task);
          const employeeInCrop: IIotEmployeeInCrop = {
            iot_crop: crop,
            iot_customer_user: assignUser.customer_user,
            name: `${crop}-${assignUser.customer_user}`,
          };
          await this.employeeInCropService.upsertEmployeeInCrop(user, employeeInCrop);
        }
      }
      // Create notification for assigned user in each task
      // Filter for task records only (those with farming_plan_state field)
      const taskRecords = query.filter((record: any) =>
        record.farming_plan_state !== undefined && record.farming_plan_state !== null
      );

      // Process notifications for each task
      for (const element of taskRecords) {
        const assign_user = element.assigned_to;

        if (assign_user) {
          // Insert on duplicate update employee_in_crop
          const crop = await getCropFromTaskName(element.name ? element.name : '');
          const employeeInCrop: IIotEmployeeInCrop = {
            iot_crop: crop,
            iot_customer_user: assign_user,
            name: `${crop}-${assign_user}`,
          };
          await this.employeeInCropService.upsertEmployeeInCrop(user, employeeInCrop);

          // Check if notification already exists
          const existNoti = await ERPExecute(
            `
            SELECT name
            FROM tabiot_notification
            WHERE entity = '${element.name}'
              AND message = 'Bạn vừa được giao công việc: ${element.label}'
            `,
            [],
          );

          if (existNoti.length === 0) {
            // Create notification for user
            const notiObject = {
              customer_user: assign_user,
              message: `Bạn vừa được giao công việc: ${element.label}`,
              created_at: moment().add(7, 'hours').format('YYYY-MM-DD HH:mm:ss'),
              entity: element.name,
              type: 'task',
              is_read: 0,
            };
            const noti: any = await this.frappeService.generalCreate({
              doc_name: 'iot_notification',
              data: notiObject,
            });

            // Push notification to mobile
            const pushNotiBody = {
              topic: `arn:aws:sns:ap-southeast-1:434827713262:${assign_user}`,
              title: 'Thông báo công việc',
              body: `Bạn vừa được giao công việc: ${element.label}`,
              noti_id: noti.data.name,
              entity: element.name ? element.name : '',
              type: 'Alarm',
            };
            await this.notificationService.publishMobileNotificationToTopicAdmin(pushNotiBody);
          }
        }
      }
      return query;
    } catch (error) {
      throw new HttpError(400, 'something went wrong went handle create array task: ' + error);
    }
  }

  async updateTaskStatusArray(user: ICurrentUser, body: FarmingPlanTaskArray) {
    try {
      console.log('body', body);
      const taskArr: FarmingPlanTask[] = body.tasks;
      //each task has format like this: {name: 'task1', status: 'status1'}
      if (!taskArr.length) {
        return 'Nothing to update, your array length is 0';
      }
      const sqlQuery: SqlQuery[] = [];
      taskArr.map((taskObj: any) => {
        const { name, status } = taskObj;
        const query = `UPDATE tabiot_farming_plan_task SET status = $1 WHERE name = $2`;
        const params = [status, name];
        sqlQuery.push({ query, params });
      });
      const updateTasksStatus = await ERPExecuteWithTransaction(sqlQuery);
      return updateTasksStatus;
    } catch (error) {
      throw new HttpError(400, 'something went wrong went handle update array task: ' + error);
    }
  }

  async updateTaskandResourceArray(user: ICurrentUser, body: FarmingPlanTaskArray) {
    try {
      const taskArray: FarmingPlanTask[] = body.tasks;

      if (taskArray.length === 0) {
        return 'Nothing to update, your array length is 0';
      }

      // Extract task-specific fields from each task object
      const taskOnlyArray = taskArray.map(
        ({ involve_in_users, todo_list, item_list, worksheet_list, prod_quantity_list, ...rest }) => rest,
      );

      // Verify if all task objects have the same fields
      const areFieldsEqual = areAllArraysEqual(taskArray.map(taskObj => Object.keys(taskObj)));

      if (!areFieldsEqual) {
        throw new HttpError(400, 'Các task object có field không giống nhau, vui lòng kiểm tra lại');
      }

      // Generate SQL queries for deleting task resources and updating tasks
      const sqlDeleteSQLTaskArray = generateDeleteSQLTaskArray(taskArray);
      const sqlUpdateTaskArray = generateUpdateSQLTaskArray(taskOnlyArray, taskArray);
      const sqlArray = [...sqlDeleteSQLTaskArray, ...sqlUpdateTaskArray];
      // Execute the transaction and handle errors
      const query = await MyERPExecuteTransactionErrorHandler(sqlArray);

      //create notification for assigned user in each task
      const element = body.tasks[0];
      const assign_user = element.assigned_to;

      //insert on duplicate update employee_in_crop
      const crop = await getCropFromTaskName(element.name ? element.name : '');
      const employeeInCrop: IIotEmployeeInCrop = {
        iot_crop: crop,
        iot_customer_user: assign_user,
        name: `${crop}-${assign_user}`,
      };
      await this.employeeInCropService.upsertEmployeeInCrop(user, employeeInCrop);
      //insert on duplicate update employee_in_crop for table iot_assign_user
      const involveInUsers = body.tasks[0].involve_in_users ? body.tasks[0].involve_in_users : [];
      for (const assignUser of involveInUsers) {
        const employeeInCrop: IIotEmployeeInCrop = {
          iot_crop: crop,
          iot_customer_user: assignUser.customer_user,
          name: assignUser.name ? assignUser.name : ``,
        };
        await this.employeeInCropService.upsertEmployeeInCrop(user, employeeInCrop);
      }
      //check xem đã có tạo notification trong hệ thống chưa
      const existNoti = await ERPExecute(
        `
      SELECT
        name
      FROM
        tabiot_notification
      WHERE
        entity = '${element.name}'
        AND message = 'Bạn vừa được giao công việc: ${element.label}'
      `,
        [],
      );
      if (existNoti.length > 0) {
        return body;
      }
      //create notification for user
      const notiObject = {
        customer_user: assign_user,
        message: `Bạn vừa được giao công việc: ${element.label}`,
        created_at: moment().add(7, 'hours').format('YYYY-MM-DD HH:mm:ss'),
        entity: element.name,
        type: 'task',
        is_read: 0,
      };
      const noti: any = await this.frappeService.generalCreate({
        doc_name: 'iot_notification',
        data: notiObject,
      });
      //push notification to mobile
      const pushNotiBody = {
        topic: `arn:aws:sns:ap-southeast-1:434827713262:${assign_user}`,
        title: 'Thông báo công việc',
        body: `Bạn vừa được giao công việc: ${element.label}`,
        noti_id: noti.data.name,
        entity: element.name ? element.name : '',
        type: 'Alarm',
      };
      await this.notificationService.publishMobileNotificationToTopicAdmin(pushNotiBody);
      return body;
    } catch (error) {
      throw new HttpError(400, 'Có lỗi xảy ra khi xử lý cập nhật mảng task: ' + error);
    }
  }

  async getTaskManagementInfo(user: ICurrentUser, params: any) {
    try {
      // verify user
      const page = params.page || 1;
      const size = params.size || 10;
      const order_by = params.order_by;
      let orderByString = ``;
      if (order_by) {
        orderByString += `ORDER BY ${order_by}`;
      }

      // check phân quyền participant của vụ mùa
      const cropParticipantList = await cropParticipantsVerify({
        customerId: user.customer_id,
        userId: user.user_id,
        sections: user.sections,
      });
      let cropSQLCondition = 'AND FALSE';
      if (cropParticipantList.length > 0) {
        const cropNames = cropParticipantList.map((d: any) => `'${d.name}'`).join(',');
        cropSQLCondition = `AND crop.name IN (${cropNames})`;
      }

      // filter
      let sqlQuery = `
        SELECT 
          task.name, 
          task.label,
          task.creation,
          task.modified,
          task.tag,
          tag.label as tag_label,
          tag.color as tag_color,
          task.image, 
          task.description, 
          task.assigned_to, 
          task.task_progress, 
          task.department_id,
          task.task_type,
          department.label as department_label,
          task.template_id,
          task.environment_template_id,
          enviroment_template.label as enviroment_template_label,
          enviroment_template.environment_code as enviroment_template_environment_code,
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', iot_user.name, 'first_name', 
                  iot_user.first_name, 'last_name', 
                  iot_user.last_name, 'user_avatar', 
                  iot_user.user_avatar
                )
              ) 
            FROM 
              tabiot_customer_user AS iot_user 
            WHERE 
              iot_user.name = task.assigned_to
          ) AS assigned_to_info, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', involve_in_users.name, 'customer_user', 
                  customer_user, 'task', task, 'first_name', 
                  first_name, 'last_name', last_name
                )
              ) 
            FROM 
              tabiot_assign_user as involve_in_users 
              LEFT JOIN tabiot_customer_user as customer_user ON involve_in_users.customer_user = customer_user.name 
            WHERE 
              involve_in_users.task = task.name
          ) AS involve_in_users, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  worksheet.name, 
                  'label', 
                  worksheet.label, 
                  'description', 
                  worksheet.description, 
                  'quantity', 
                  worksheet.quantity, 
                  'type', 
                  worksheet.type, 
                  'task_id', 
                  worksheet.task_id, 
                  'exp_quantity', 
                  worksheet.exp_quantity,
                  'cost',
                  worksheet.cost, 
                  'work_type_id', 
                  work_type_id, 
                  'work_type', 
                  (
                    SELECT 
                      json_build_object(
                        'name', name, 'label', label, 'customer_id', 
                        customer_id
                      ) 
                    FROM 
                      tabiot_task_work_type as work_type 
                    WHERE 
                      work_type.name = worksheet.work_type_id
                  )
                )
              ) 
            FROM 
              tabiot_farming_plan_task_worksheet AS worksheet 
            WHERE 
              worksheet.task_id = task.name
          ) AS worksheet_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  item_used.name, 
                  'quantity', 
                  item_used.quantity, 
                  'exp_quantity', 
                  item_used.exp_quantity, 
                  'loss_quantity', 
                  item_used.loss_quantity,
                  'issued_quantity',
                  item_used.issued_quantity,
                  'draft_quantity',
                  item_used.draft_quantity, 
                  'description', 
                  item_used.description, 
                  'task_id', 
                  item_used.task_id, 
                  'iot_category_id', 
                  item_used.iot_category_id,
                  'label',
                  item.label,
                  'item_name',
                  item.item_name,
                  'active_uom',
                  item_used.active_uom,
                  'active_uom_name',
                  active_uom.uom_name,
                  'active_conversion_factor',
                  item_used.active_conversion_factor,
                  'uom_name',
                  uom.uom_name,
                  'uom_id',
                  uom.name,
                  'conversion_factor',
                  1,
                  'uoms', (
                    SELECT json_agg(
                      json_build_object(
                        'uom_id', uom_conv.uom,
                        'uom_label', uom_table.uom_name,
                        'conversion_factor', uom_conv.conversion_factor
                      )
                    )
                    FROM "tabUOM Conversion Detail" AS uom_conv
                    LEFT JOIN "tabUOM" AS uom_table ON uom_conv.uom = uom_table.name
                    WHERE uom_conv.parent = item_used.iot_category_id
                  )
                )
              ) 
            FROM 
              tabiot_warehouse_item_task_used AS item_used
            LEFT JOIN "tabItem" AS item ON item.name = item_used.iot_category_id
            LEFT JOIN "tabUOM" AS uom ON item.stock_uom = uom.name
            LEFT JOIN "tabUOM" AS active_uom ON item_used.active_uom = active_uom.name
            WHERE 
              item_used.task_id = task.name
          ) AS item_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', todo.name, 'label', todo.label, 
                  'status', todo.status, 'description', 
                  todo.description, 'task_id', todo.farming_plan_task, 
                  'image', todo.image, 'start_date', 
                  todo.start_date, 'end_date', todo.end_date, 
                  'is_completed', todo.is_completed,
                  'customer_user_id', todo.customer_user_id,
                  'customer_user_name', CONCAT(cus_user.first_name, ' ', cus_user.last_name)
                )
              ) 
            FROM 
              tabiot_todo AS todo
            LEFT JOIN
              tabiot_customer_user AS cus_user
            ON todo.customer_user_id = cus_user.name
            WHERE 
              todo.farming_plan_task = task.name
          ) AS todo_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  prod_quantity.name, 
                  'quantity', 
                  prod_quantity.quantity, 
                  'exp_quantity', 
                  prod_quantity.exp_quantity,
                  'lost_quantity', 
                  prod_quantity.lost_quantity,
                  'issued_quantity',
                  prod_quantity.issued_quantity,
                  'draft_quantity',
                  prod_quantity.draft_quantity,
                  'finished_quantity',
                  prod_quantity.finished_quantity,
                  'description', 
                  prod_quantity.description, 
                  'task_id', 
                  prod_quantity.task_id, 
                  'product_id', 
                  prod_quantity.product_id,
                  'label',
                  item.label,
                  'item_name',
                  item.item_name,
                   'active_uom',
                  prod_quantity.active_uom,
                  'active_uom_name',
                  active_uom.uom_name,
                  'active_conversion_factor',
                  prod_quantity.active_conversion_factor,
                  'uom_name',
                  uom.uom_name,
                  'uom_id',
                  uom.name,
                  'conversion_factor',
                  1,
                  'uoms', (
                    SELECT json_agg(
                      json_build_object(
                        'uom_id', uom_conv.uom,
                        'uom_label', uom_table.uom_name,
                        'conversion_factor', uom_conv.conversion_factor
                      )
                    )
                    FROM "tabUOM Conversion Detail" AS uom_conv
                    LEFT JOIN "tabUOM" AS uom_table ON uom_conv.uom = uom_table.name
                    WHERE uom_conv.parent = prod_quantity.product_id
                  )
                )
              ) 
            FROM 
              tabiot_production_quantity AS prod_quantity 
            LEFT JOIN "tabItem" AS item ON item.name = prod_quantity.product_id
            LEFT JOIN "tabUOM" AS uom ON item.stock_uom = uom.name
            LEFT JOIN "tabUOM" AS active_uom ON prod_quantity.active_uom = active_uom.name
            WHERE 
              prod_quantity.task_id = task.name
          ) AS prod_quantity_list, 
          TO_CHAR(task.start_date, 'YYYY-MM-DD HH24:MI:SS') as start_date, 
          TO_CHAR(task.end_date, 'YYYY-MM-DD HH24:MI:SS') as end_date, 
          task.status,
          (
            SELECT json_build_object(
                'label', status.label,
                'color', status.color,
                'customer_id', status.customer_id
            )
            FROM tabiot_task_status AS status
            WHERE status.name = task.status
          ) AS status_detail, 
          pl_state.name AS farming_plan_state, 
          pl_state.label AS state_name, 
          pl.name AS farming_plan, 
          pl.label AS plan_name, 
          crop.name AS crop_id, 
          crop.label AS crop_name, 
          crop.status AS crop_status,
          zone.name AS zone_id, 
          zone.label AS zone_name, 
          project.name AS project_id, 
          project.label AS project_name, 
          zone.customer_id AS customer_id, 
          COUNT (todo.*) AS todo_total, 
          COUNT (
            CASE WHEN todo.is_completed = 1 THEN 1 END
          ) AS todo_done,
          crop_tag.name AS crop_tag_name,
          crop_tag.label AS crop_tag_label,
          crop_tag.color AS crop_tag_color
        FROM 
          tabiot_farming_plan_task AS task 
          LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name 
          LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name 
          LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name 
          LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name 
          LEFT JOIN tabiot_project AS project ON zone.project_id = project.name 
          LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
          LEFT JOIN tabiot_tag AS tag ON tag.name = task.tag
          LEFT JOIN tabiot_tag AS crop_tag ON crop_tag.name = crop.tag
          LEFT JOIN department AS department ON task.department_id = department.name
          LEFT JOIN tabiot_farming_plan_environment_template AS enviroment_template ON task.environment_template_id = enviroment_template.name
        WHERE
              zone.customer_id = '${user.customer_id}'
              AND crop.is_deleted = 0 AND pl.is_deleted = 0
              AND task.added_in_diary = 0
              AND crop.is_template = 0
              ${cropSQLCondition}
      `;

      let filters = params.filters ? JSON.parse(params.filters) : [];
      let sqlConditions: string = '';
      if (filters.length && filters[0].length) {
        sqlConditions += this.otherService.buildTaskManageSQLConditions(filters, 'filters');
      }

      sqlQuery += sqlConditions;
      sqlQuery += `GROUP BY 
                                task.name, 
                                pl_state.name, 
                                pl.name, 
                                crop.name, 
                                zone.name, 
                                project.name,
                                tag.label,
                                tag.color,
                                department.name,
                                enviroment_template.name,
                                crop_tag.name
                                `;
      // pagination
      sqlQuery += `
                ${orderByString}
                OFFSET ${size} * (${page} - 1) LIMIT ${size};
               
                `;
      const taskList = await ERPExecute(sqlQuery, []);
      for (const task of taskList) {
        sortByLabel(task.todo_list);
        sortByLabel(task.worksheet_list);
        sortByLabel(task.prod_quantity_list);
        sortByProperty(task.item_list, 'category');
      }

      const totalElementsResult = await ERPExecute(
        `
                select count(task.name) as count_task from tabiot_farming_plan_task AS task
                LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name
                LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name
                LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name
                LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name
                LEFT JOIN tabiot_project AS project ON zone.project_id = project.name
                LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
                LEFT JOIN tabiot_tag as tag on tag.name = task.tag
                LEFT JOIN department as department on department.name = task.department_id
              WHERE
               zone.customer_id = '${user.customer_id}'
                AND crop.is_deleted = 0 AND pl.is_deleted = 0
                AND task.added_in_diary = 0
                AND crop.is_template = 0
                ${cropSQLCondition}
                ${sqlConditions}
              GROUP BY 
                task.name, 
                pl_state.name, 
                pl.name, 
                crop.name, 
                zone.name, 
                project.name,
                tag.label,
                tag.color,
                department.name
                `,
        [],
      );
      const totalElements = totalElementsResult.length || 0;
      const totalPages = Math.ceil(totalElements / size);
      const formattedResult = taskList.map(item => ({
        ...item,
        task_progress: parseFloat(item.task_progress),
      }));
      const pagination = {
        pageNumber: parseInt(page),
        pageSize: parseInt(size),
        totalElements: totalElements,
        totalPages: totalPages,
      };

      const response = {
        data: formattedResult,
        pagination: pagination,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }


  async getTaskManagementInfoAll(user: ICurrentUser, params: any) {
    try {
      /**
       * verify user
       */
      // Extract page and size parameters from the input
      const page = params.page || 1;
      const size = params.size || 10; // Default page size to 10 if not provided
      const order_by = params.order_by;
      let orderByString = ``;
      if (order_by) {
        orderByString += `ORDER BY ${order_by}`;
      }

      //check phân quyền participant của vụ mùa
      const cropParticipantList = await cropParticipantsVerify({
        customerId: user.customer_id,
        userId: user.user_id,
        sections: user.sections,
      });
      let cropSQLCondition = 'AND FALSE';
      if (cropParticipantList.length > 0) {
        const cropNames = cropParticipantList.map((d: any) => `'${d.name}'`).join(',');
        cropSQLCondition = `AND crop.name IN (${cropNames})`;
      }
      //filter
      let sqlQuery = `
        SELECT 
          task.name, 
          task.label,
          task.tag,
          tag.label as tag_label,
          tag.color as tag_color,
          task.image, 
          task.description, 
          task.assigned_to, 
          task.task_progress, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', iot_user.name, 'first_name', 
                  iot_user.first_name, 'last_name', 
                  iot_user.last_name, 'user_avatar', 
                  iot_user.user_avatar
                )
              ) 
            FROM 
              tabiot_customer_user AS iot_user 
            WHERE 
              iot_user.name = task.assigned_to
          ) AS assigned_to_info, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', involve_in_users.name, 'customer_user', 
                  customer_user, 'task', task, 'first_name', 
                  first_name, 'last_name', last_name
                )
              ) 
            FROM 
              tabiot_assign_user as involve_in_users 
              LEFT JOIN tabiot_customer_user as customer_user ON involve_in_users.customer_user = customer_user.name 
            WHERE 
              involve_in_users.task = task.name
          ) AS involve_in_users, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  worksheet.name, 
                  'label', 
                  worksheet.label, 
                  'description', 
                  worksheet.description, 
                  'quantity', 
                  worksheet.quantity, 
                  'type', 
                  worksheet.type, 
                  'task_id', 
                  worksheet.task_id, 
                  'exp_quantity', 
                  worksheet.exp_quantity,
                  'cost',
                  worksheet.cost, 
                  'work_type_id', 
                  work_type_id, 
                  'work_type', 
                  (
                    SELECT 
                      json_build_object(
                        'name', name, 'label', label, 'customer_id', 
                        customer_id
                      ) 
                    FROM 
                      tabiot_task_work_type as work_type 
                    WHERE 
                      work_type.name = worksheet.work_type_id
                  )
                )
              ) 
            FROM 
              tabiot_farming_plan_task_worksheet AS worksheet 
            WHERE 
              worksheet.task_id = task.name
          ) AS worksheet_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  item_used.name, 
                  'quantity', 
                  item_used.quantity, 
                  'exp_quantity', 
                  item_used.exp_quantity, 
                  'loss_quantity', 
                  item_used.loss_quantity,
                  'draft_quantity',
                  item_used.draft_quantity, 
                  'description', 
                  item_used.description, 
                  'task_id', 
                  item_used.task_id, 
                  'iot_category_id', 
                  item_used.iot_category_id,
                  'label',
                  item.label,
                  'item_name',
                  item.item_name,
                  'uom_name',
                  uom.uom_name,
                  'uom_id',
                  uom.name,
                  'conversion_factor',
                  1,
                  'uoms', (
                    SELECT json_agg(
                      json_build_object(
                        'uom_id', uom_conv.uom,
                        'uom_label', uom_table.uom_name,
                        'conversion_factor', uom_conv.conversion_factor
                      )
                    )
                    FROM "tabUOM Conversion Detail" AS uom_conv
                    LEFT JOIN "tabUOM" AS uom_table ON uom_conv.uom = uom_table.name
                    WHERE uom_conv.parent = item_used.iot_category_id
                  )
                )
              ) 
            FROM 
              tabiot_warehouse_item_task_used AS item_used
            LEFT JOIN "tabItem" AS item ON item.name = item_used.iot_category_id
            LEFT JOIN "tabUOM" AS uom ON item.stock_uom = uom.name
            WHERE 
              item_used.task_id = task.name
          ) AS item_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', todo.name, 'label', todo.label, 
                  'status', todo.status, 'description', 
                  todo.description, 'task_id', todo.farming_plan_task, 
                  'image', todo.image, 'start_date', 
                  todo.start_date, 'end_date', todo.end_date, 
                  'is_completed', todo.is_completed,
                  'customer_user_id', todo.customer_user_id,
                  'customer_user_name', CONCAT(cus_user.first_name, ' ', cus_user.last_name)
                )
              ) 
            FROM 
              tabiot_todo AS todo
            LEFT JOIN
              tabiot_customer_user AS cus_user
            ON todo.customer_user_id = cus_user.name
            WHERE 
              todo.farming_plan_task = task.name
          ) AS todo_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  prod_quantity.name, 
                  'quantity', 
                  prod_quantity.quantity, 
                  'exp_quantity', 
                  prod_quantity.exp_quantity,
                  'lost_quantity', 
                  prod_quantity.lost_quantity,
                  'draft_quantity',
                  prod_quantity.draft_quantity,
                  'description', 
                  prod_quantity.description, 
                  'task_id', 
                  prod_quantity.task_id, 
                  'product_id', 
                  prod_quantity.product_id,
                  'label',
                  item.label,
                  'item_name',
                  item.item_name,
                  'uom_name',
                  uom.uom_name,
                  'uom_id',
                  uom.name,
                  'conversion_factor',
                  1,
                  'uoms', (
                    SELECT json_agg(
                      json_build_object(
                        'uom_id', uom_conv.uom,
                        'uom_label', uom_table.uom_name,
                        'conversion_factor', uom_conv.conversion_factor
                      )
                    )
                    FROM "tabUOM Conversion Detail" AS uom_conv
                    LEFT JOIN "tabUOM" AS uom_table ON uom_conv.uom = uom_table.name
                    WHERE uom_conv.parent = prod_quantity.product_id
                  )
                )
              ) 
            FROM 
              tabiot_production_quantity AS prod_quantity 
            LEFT JOIN "tabItem" AS item ON item.name = prod_quantity.product_id
            LEFT JOIN "tabUOM" AS uom ON item.stock_uom = uom.name
            WHERE 
              prod_quantity.task_id = task.name
          ) AS prod_quantity_list, 
          TO_CHAR(task.start_date, 'YYYY-MM-DD HH24:MI:SS') as start_date, 
          TO_CHAR(task.end_date, 'YYYY-MM-DD HH24:MI:SS') as end_date, 
          task.status,
          (
            SELECT json_build_object(
                'label', status.label,
                'color', status.color,
                'customer_id', status.customer_id
            )
            FROM tabiot_task_status AS status
            WHERE status.name = task.status
          ) AS status_detail, 
          pl_state.name AS farming_plan_state, 
          pl_state.label AS state_name, 
          pl.name AS farming_plan, 
          pl.label AS plan_name, 
          crop.name AS crop_id, 
          crop.label AS crop_name, 
          crop.status AS crop_status,
          crop.is_template AS is_template,
          zone.name AS zone_id, 
          zone.label AS zone_name, 
          project.name AS project_id, 
          project.label AS project_name, 
          zone.customer_id AS customer_id, 
          COUNT (todo.*) AS todo_total, 
          COUNT (
            CASE WHEN todo.is_completed = 1 THEN 1 END
          ) AS todo_done 
        FROM 
          tabiot_farming_plan_task AS task 
          LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name 
          LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name 
          LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name 
          LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name 
          LEFT JOIN tabiot_project AS project ON zone.project_id = project.name 
          LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
          LEFT JOIN tabiot_tag AS tag ON tag.name = task.tag
        WHERE

                zone.customer_id = '${user.customer_id}'
                AND crop.is_deleted = 0 AND pl.is_deleted = 0
                AND task.added_in_diary = 0
                ${cropSQLCondition}
                  `;

      let filters = params.filters ? JSON.parse(params.filters) : [];
      let sqlConditions: string = '';
      if (filters.length && filters[0].length) {
        sqlConditions += this.otherService.buildTaskManageSQLConditions(filters, 'filters');
      }

      sqlQuery += sqlConditions;
      sqlQuery += `GROUP BY 
                                task.name, 
                                pl_state.name, 
                                pl.name, 
                                crop.name, 
                                zone.name, 
                                project.name,
                                tag.label,
                                tag.color
                                `;
      //pagination
      sqlQuery += `
                ${orderByString}
                OFFSET ${size} * (${page} - 1) LIMIT ${size};
               
                `;
      const taskList = await ERPExecute(sqlQuery, []);
      for (const task of taskList) {
        sortByLabel(task.todo_list);
        sortByLabel(task.worksheet_list);
        sortByLabel(task.prod_quantity_list);
        sortByProperty(task.item_list, 'category');
      }

      const totalElementsResult = await ERPExecute(
        `
                select count(task.name) as count_task from tabiot_farming_plan_task AS task
                LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name
                LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name
                LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name
                LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name
                LEFT JOIN tabiot_project AS project ON zone.project_id = project.name
                LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
                LEFT JOIN tabiot_tag as tag on tag.name = task.tag
              WHERE
               zone.customer_id = '${user.customer_id}'
                AND crop.is_deleted = 0 AND pl.is_deleted = 0
                AND task.added_in_diary = 0
                ${cropSQLCondition}
                ${sqlConditions}
              GROUP BY 
                task.name, 
                pl_state.name, 
                pl.name, 
                crop.name, 
                zone.name, 
                project.name,
                tag.label,
                tag.color
                `,
        [],
      );
      const totalElements = totalElementsResult.length || 0;
      // Calculate total pages
      const totalPages = Math.ceil(totalElements / size);
      // Slice the taskList to include only the data for the requested page
      // const startIndex = (page - 1) * size;
      // const endIndex = startIndex + size;
      // const dataForPage = taskList.slice(startIndex, endIndex);
      const formattedResult = taskList.map(item => ({
        ...item,
        task_progress: parseFloat(item.task_progress),
      }));
      // Create pagination metadata
      const pagination = {
        pageNumber: parseInt(page),
        pageSize: parseInt(size),
        totalElements: totalElements,
        totalPages: totalPages,
      };

      // Create the response format
      const response = {
        data: formattedResult,
        pagination: pagination,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  async getTemplateTaskManagementInfo(user: ICurrentUser, params: any) {
    try {
      /**
       * verify user
       */
      // Extract page and size parameters from the input
      const page = params.page || 1;
      const size = params.size || 10; // Default page size to 10 if not provided
      const order_by = params.order_by;
      let orderByString = ``;
      if (order_by) {
        orderByString += `ORDER BY ${order_by}`;
      }

      //check phân quyền participant của vụ mùa
      const cropParticipantList = await cropParticipantsVerify({
        customerId: user.customer_id,
        userId: user.user_id,
        sections: user.sections,
      });
      let cropSQLCondition = 'AND FALSE';
      if (cropParticipantList.length > 0) {
        const cropNames = cropParticipantList.map((d: any) => `'${d.name}'`).join(',');
        cropSQLCondition = `AND crop.name IN (${cropNames})`;
      }
      //filter
      let sqlQuery = `
        SELECT 
          task.name, 
          task.label,
          task.tag,
          tag.label as tag_label,
          tag.color as tag_color,
          task.image, 
          task.description, 
          task.assigned_to, 
          task.task_progress, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', iot_user.name, 'first_name', 
                  iot_user.first_name, 'last_name', 
                  iot_user.last_name, 'user_avatar', 
                  iot_user.user_avatar
                )
              ) 
            FROM 
              tabiot_customer_user AS iot_user 
            WHERE 
              iot_user.name = task.assigned_to
          ) AS assigned_to_info, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', involve_in_users.name, 'customer_user', 
                  customer_user, 'task', task, 'first_name', 
                  first_name, 'last_name', last_name
                )
              ) 
            FROM 
              tabiot_assign_user as involve_in_users 
              LEFT JOIN tabiot_customer_user as customer_user ON involve_in_users.customer_user = customer_user.name 
            WHERE 
              involve_in_users.task = task.name
          ) AS involve_in_users, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  worksheet.name, 
                  'label', 
                  worksheet.label, 
                  'description', 
                  worksheet.description, 
                  'quantity', 
                  worksheet.quantity, 
                  'type', 
                  worksheet.type, 
                  'task_id', 
                  worksheet.task_id, 
                  'exp_quantity', 
                  worksheet.exp_quantity,
                  'cost',
                  worksheet.cost, 
                  'work_type_id', 
                  work_type_id, 
                  'work_type', 
                  (
                    SELECT 
                      json_build_object(
                        'name', name, 'label', label, 'customer_id', 
                        customer_id
                      ) 
                    FROM 
                      tabiot_task_work_type as work_type 
                    WHERE 
                      work_type.name = worksheet.work_type_id
                  )
                )
              ) 
            FROM 
              tabiot_farming_plan_task_worksheet AS worksheet 
            WHERE 
              worksheet.task_id = task.name
          ) AS worksheet_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  item_used.name, 
                  'quantity', 
                  item_used.quantity, 
                  'exp_quantity', 
                  item_used.exp_quantity, 
                  'loss_quantity', 
                  item_used.loss_quantity,
                  'draft_quantity',
                  item_used.draft_quantity, 
                  'description', 
                  item_used.description, 
                  'task_id', 
                  item_used.task_id, 
                  'iot_category_id', 
                  item_used.iot_category_id,
                  'label',
                  item.label,
                  'item_name',
                  item.item_name,
                  'uom_name',
                  uom.uom_name,
                  'uom_id',
                  uom.name,
                  'conversion_factor',
                  1,
                  'uoms', (
                    SELECT json_agg(
                      json_build_object(
                        'uom_id', uom_conv.uom,
                        'uom_label', uom_table.uom_name,
                        'conversion_factor', uom_conv.conversion_factor
                      )
                    )
                    FROM "tabUOM Conversion Detail" AS uom_conv
                    LEFT JOIN "tabUOM" AS uom_table ON uom_conv.uom = uom_table.name
                    WHERE uom_conv.parent = item_used.iot_category_id
                  )
                )
              ) 
            FROM 
              tabiot_warehouse_item_task_used AS item_used
            LEFT JOIN "tabItem" AS item ON item.name = item_used.iot_category_id
            LEFT JOIN "tabUOM" AS uom ON item.stock_uom = uom.name
            WHERE 
              item_used.task_id = task.name
          ) AS item_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', todo.name, 'label', todo.label, 
                  'status', todo.status, 'description', 
                  todo.description, 'task_id', todo.farming_plan_task, 
                  'image', todo.image, 'start_date', 
                  todo.start_date, 'end_date', todo.end_date, 
                  'is_completed', todo.is_completed,
                  'customer_user_id', todo.customer_user_id,
                  'customer_user_name', CONCAT(cus_user.first_name, ' ', cus_user.last_name)
                )
              ) 
            FROM 
              tabiot_todo AS todo
            LEFT JOIN
              tabiot_customer_user AS cus_user
            ON todo.customer_user_id = cus_user.name
            WHERE 
              todo.farming_plan_task = task.name
          ) AS todo_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  prod_quantity.name, 
                  'quantity', 
                  prod_quantity.quantity, 
                  'exp_quantity', 
                  prod_quantity.exp_quantity,
                  'lost_quantity', 
                  prod_quantity.lost_quantity,
                  'draft_quantity',
                  prod_quantity.draft_quantity,
                  'description', 
                  prod_quantity.description, 
                  'task_id', 
                  prod_quantity.task_id, 
                  'product_id', 
                  prod_quantity.product_id,
                  'label',
                  item.label,
                  'item_name',
                  item.item_name,
                  'uom_name',
                  uom.uom_name,
                  'uom_id',
                  uom.name,
                  'conversion_factor',
                  1,
                  'uoms', (
                    SELECT json_agg(
                      json_build_object(
                        'uom_id', uom_conv.uom,
                        'uom_label', uom_table.uom_name,
                        'conversion_factor', uom_conv.conversion_factor
                      )
                    )
                    FROM "tabUOM Conversion Detail" AS uom_conv
                    LEFT JOIN "tabUOM" AS uom_table ON uom_conv.uom = uom_table.name
                    WHERE uom_conv.parent = prod_quantity.product_id
                  )
                )
              ) 
            FROM 
              tabiot_production_quantity AS prod_quantity 
            LEFT JOIN "tabItem" AS item ON item.name = prod_quantity.product_id
            LEFT JOIN "tabUOM" AS uom ON item.stock_uom = uom.name
            WHERE 
              prod_quantity.task_id = task.name
          ) AS prod_quantity_list, 
          TO_CHAR(task.start_date, 'YYYY-MM-DD HH24:MI:SS') as start_date, 
          TO_CHAR(task.end_date, 'YYYY-MM-DD HH24:MI:SS') as end_date, 
          task.status,
          (
            SELECT json_build_object(
                'label', status.label,
                'color', status.color,
                'customer_id', status.customer_id
            )
            FROM tabiot_task_status AS status
            WHERE status.name = task.status
          ) AS status_detail, 
          pl_state.name AS farming_plan_state, 
          pl_state.label AS state_name, 
          pl.name AS farming_plan, 
          pl.label AS plan_name, 
          crop.name AS crop_id, 
          crop.label AS crop_name, 
          crop.status AS crop_status,
          zone.name AS zone_id, 
          zone.label AS zone_name, 
          project.name AS project_id, 
          project.label AS project_name, 
          zone.customer_id AS customer_id, 
          COUNT (todo.*) AS todo_total, 
          COUNT (
            CASE WHEN todo.is_completed = 1 THEN 1 END
          ) AS todo_done 
        FROM 
          tabiot_farming_plan_task AS task 
          LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name 
          LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name 
          LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name 
          LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name 
          LEFT JOIN tabiot_project AS project ON zone.project_id = project.name 
          LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
          LEFT JOIN tabiot_tag AS tag ON tag.name = task.tag
        WHERE

                zone.customer_id = '${user.customer_id}'
                AND crop.is_deleted = 0 AND pl.is_deleted = 0
                AND task.added_in_diary = 0
                AND crop.is_template = 1
                ${cropSQLCondition}
                  `;

      let filters = params.filters ? JSON.parse(params.filters) : [];
      let sqlConditions: string = '';
      if (filters.length && filters[0].length) {
        sqlConditions += this.otherService.buildTaskManageSQLConditions(filters, 'filters');
      }

      sqlQuery += sqlConditions;
      sqlQuery += `GROUP BY 
                                task.name, 
                                pl_state.name, 
                                pl.name, 
                                crop.name, 
                                zone.name, 
                                project.name,
                                tag.label,
                                tag.color`;
      //pagination
      sqlQuery += `
                ${orderByString}
                OFFSET ${size} * (${page} - 1) LIMIT ${size};
               
                `;
      const taskList = await ERPExecute(sqlQuery, []);
      for (const task of taskList) {
        sortByLabel(task.todo_list);
        sortByLabel(task.worksheet_list);
        sortByLabel(task.prod_quantity_list);
        sortByProperty(task.item_list, 'category');
      }

      const totalElementsResult = await ERPExecute(
        `
                select count(task.name) as count_task from tabiot_farming_plan_task AS task
                LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name
                LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name
                LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name
                LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name
                LEFT JOIN tabiot_project AS project ON zone.project_id = project.name
                LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
                LEFT JOIN tabiot_tag as tag on tag.name = task.tag
              WHERE
                zone.customer_id = '${user.customer_id}'
                AND crop.is_deleted = 0 AND pl.is_deleted = 0
                AND task.added_in_diary = 0
                AND crop.is_template = 1
                ${cropSQLCondition}
                ${sqlConditions}
              GROUP BY 
                task.name, 
                pl_state.name, 
                pl.name, 
                crop.name, 
                zone.name, 
                project.name,
                tag.label,
                tag.color
                `,
        [],
      );
      const totalElements = totalElementsResult.length || 0;
      // Calculate total pages
      const totalPages = Math.ceil(totalElements / size);
      // Slice the taskList to include only the data for the requested page
      // const startIndex = (page - 1) * size;
      // const endIndex = startIndex + size;
      // const dataForPage = taskList.slice(startIndex, endIndex);
      const formattedResult = taskList.map(item => ({
        ...item,
        task_progress: parseFloat(item.task_progress),
      }));
      // Create pagination metadata
      const pagination = {
        pageNumber: parseInt(page),
        pageSize: parseInt(size),
        totalElements: totalElements,
        totalPages: totalPages,
      };

      // Create the response format
      const response = {
        data: formattedResult,
        pagination: pagination,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  async getTaskManagementInfoInDiary(user: ICurrentUser, params: any) {
    try {
      console.log('params', params);
      /**
       * verify user
       */
      // Extract page and size parameters from the input
      const page = params.page || 1;
      const size = params.size || 10; // Default page size to 10 if not provided
      const order_by = params.order_by;
      let orderByString = ``;
      if (order_by) {
        orderByString += `ORDER BY ${order_by}`;
      }
      //check phân quyền participant của vụ mùa
      const cropParticipantList = await cropParticipantsVerify({
        customerId: user.customer_id,
        userId: user.user_id,
        sections: user.sections,
      });
      let cropSQLCondition = 'AND FALSE';
      if (cropParticipantList.length > 0) {
        const cropNames = cropParticipantList.map((d: any) => `'${d.name}'`).join(',');
        cropSQLCondition = `AND crop.name IN (${cropNames})`;
      }
      //filter
      let sqlQuery = `
                SELECT 
          task.name, 
          task.label,
          task.tag,
          tag.label as tag_label,
          tag.color as tag_color,
          task.image, 
          task.description, 
          task.assigned_to, 
          task.task_progress,
          task.text_state,
          task.text_plan,
          task.text_assign_user, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', iot_user.name, 'first_name', 
                  iot_user.first_name, 'last_name', 
                  iot_user.last_name, 'user_avatar', 
                  iot_user.user_avatar
                )
              ) 
            FROM 
              tabiot_customer_user AS iot_user 
            WHERE 
              iot_user.name = task.assigned_to
          ) AS assigned_to_info, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', involve_in_users.name, 'customer_user', 
                  customer_user, 'task', task, 'first_name', 
                  first_name, 'last_name', last_name
                )
              ) 
            FROM 
              tabiot_assign_user as involve_in_users 
              LEFT JOIN tabiot_customer_user as customer_user ON involve_in_users.customer_user = customer_user.name 
            WHERE 
              involve_in_users.task = task.name
          ) AS involve_in_users, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  worksheet.name, 
                  'label', 
                  worksheet.label, 
                  'description', 
                  worksheet.description, 
                  'quantity', 
                  worksheet.quantity, 
                  'type', 
                  worksheet.type, 
                  'task_id', 
                  worksheet.task_id, 
                  'exp_quantity', 
                  worksheet.exp_quantity,
                  'cost',
                  worksheet.cost, 
                  'work_type_id', 
                  work_type_id, 
                  'work_type', 
                  (
                    SELECT 
                      json_build_object(
                        'name', name, 'label', label, 'customer_id', 
                        customer_id
                      ) 
                    FROM 
                      tabiot_task_work_type as work_type 
                    WHERE 
                      work_type.name = worksheet.work_type_id
                  )
                )
              ) 
            FROM 
              tabiot_farming_plan_task_worksheet AS worksheet 
            WHERE 
              worksheet.task_id = task.name
          ) AS worksheet_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  item_used.name, 
                  'quantity', 
                  item_used.quantity, 
                  'exp_quantity', 
                  item_used.exp_quantity, 
                  'loss_quantity', 
                  item_used.loss_quantity, 
                  'description', 
                  item_used.description, 
                  'task_id', 
                  item_used.task_id, 
                  'iot_category_id', 
                  item_used.iot_category_id, 
                  'category', 
                  (
                    SELECT 
                      json_build_object(
                        'label', 
                        label, 
                        'category_group', 
                        category_group,
                        'image',
                        image,
                        'image',
                        image,
                        'item_unit_id', 
                        item_unit_id, 
                        'item_unit', 
                        (
                          SELECT 
                            json_build_object(
                              'label', label, 'short_label', short_label, 
                              'customer_id', customer_id
                            ) 
                          FROM 
                            tabiot_item_unit as item_unit 
                          WHERE 
                            item_unit.name = category.item_unit_id
                        )
                      ) 
                    FROM 
                      tabiot_category as category 
                    WHERE 
                      category.name = item_used.iot_category_id
                  )
                )
              ) 
            FROM 
              tabiot_warehouse_item_task_used AS item_used 
            WHERE 
              item_used.task_id = task.name
          ) AS item_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', todo.name, 'label', todo.label, 
                  'status', todo.status, 'description', 
                  todo.description, 'task_id', todo.farming_plan_task, 
                  'image', todo.image, 'start_date', 
                  todo.start_date, 'end_date', todo.end_date, 
                  'is_completed', todo.is_completed,
                  'customer_user_id', todo.customer_user_id,
                  'customer_user_name', CONCAT(cus_user.first_name, ' ', cus_user.last_name)
                )
              ) 
            FROM 
              tabiot_todo AS todo
            LEFT JOIN
              tabiot_customer_user AS cus_user
            ON todo.customer_user_id = cus_user.name
            WHERE 
              todo.farming_plan_task = task.name
          ) AS todo_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  prod_quantity.name, 
                  'quantity', 
                  prod_quantity.quantity, 
                  'exp_quantity', 
                  prod_quantity.exp_quantity,
                  'lost_quantity', 
                  prod_quantity.lost_quantity,  
                  'description', 
                  prod_quantity.description, 
                  'task_id', 
                  prod_quantity.task_id, 
                  'product_id', 
                  prod_quantity.product_id, 
                  'agriculture_product', 
                  (
                    SELECT 
                      json_build_object(
                        'name', name, 'label', label, 'image', 
                        image, 'unit', unit, 'unit_label', 
                        unit_label, 'packing_unit', packing_unit, 
                        'packing_unit_label', packing_unit_label
                      ) 
                    FROM 
                      tabiot_agriculture_product as product 
                    WHERE 
                      product.name = prod_quantity.product_id
                  )
                )
              ) 
            FROM 
              tabiot_production_quantity AS prod_quantity 
            WHERE 
              prod_quantity.task_id = task.name
          ) AS prod_quantity_list, 
          TO_CHAR(task.start_date, 'YYYY-MM-DD HH24:MI:SS') as start_date, 
          TO_CHAR(task.end_date, 'YYYY-MM-DD HH24:MI:SS') as end_date, 
          task.status,
          (
            SELECT json_build_object(
                'label', status.label,
                'color', status.color,
                'customer_id', status.customer_id
            )
            FROM tabiot_task_status AS status
            WHERE status.name = task.status
          ) AS status_detail, 
          pl_state.name AS farming_plan_state, 
          pl_state.label AS state_name, 
          pl.name AS farming_plan, 
          pl.label AS plan_name, 
          crop.name AS crop_id, 
          crop.label AS crop_name, 
          crop.status AS crop_status,
          zone.name AS zone_id, 
          zone.label AS zone_name, 
          project.name AS project_id, 
          project.label AS project_name, 
          project.customer_id AS customer_id, 
          COUNT (todo.*) AS todo_total, 
          COUNT (
            CASE WHEN todo.is_completed = 1 THEN 1 END
          ) AS todo_done 
        FROM 
          tabiot_farming_plan_task AS task 
          LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name 
          LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name 
          LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name 
          LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name 
          LEFT JOIN tabiot_project AS project ON zone.project_id = project.name 
          LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
          LEFT JOIN tabiot_tag AS tag ON tag.name = task.tag
        WHERE

                zone.customer_id = '${user.customer_id}'
                AND crop.is_deleted = 0 AND pl.is_deleted = 0
                AND (task.enable_origin_tracing = 1 OR task.added_in_diary = 1)
                ${cropSQLCondition}
                  `;

      let filters = params.filters ? JSON.parse(params.filters) : [];
      let sqlConditions: string = '';
      if (filters.length && filters[0].length) {
        sqlConditions += this.otherService.buildTaskManageSQLConditions(filters, 'filters');
      }

      sqlQuery += sqlConditions;
      sqlQuery += `GROUP BY 
                                task.name, 
                                pl_state.name, 
                                pl.name, 
                                crop.name, 
                                zone.name, 
                                project.name,
                                tag.label,
                                tag.color`;
      //pagination
      sqlQuery += `
                ${orderByString}
                OFFSET ${size} * (${page} - 1) LIMIT ${size};
               
                `;
      const taskList = await ERPExecute(sqlQuery, []);
      for (const task of taskList) {
        sortByLabel(task.todo_list);
        sortByLabel(task.worksheet_list);
        sortByLabel(task.prod_quantity_list);
        sortByProperty(task.item_list, 'category');
      }

      const totalElementsResult = await ERPExecute(
        `
                select count(task.name) as count_task from tabiot_farming_plan_task AS task
                LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name
                LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name
                LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name
                LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name
                LEFT JOIN tabiot_project AS project ON zone.project_id = project.name
                LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
                LEFT JOIN tabiot_tag as tag on tag.name = task.tag
              WHERE
                project.customer_id = '${user.customer_id}'
                AND crop.is_deleted = 0 AND pl.is_deleted = 0
                ${cropSQLCondition}
                ${sqlConditions}
              GROUP BY 
                task.name, 
                pl_state.name, 
                pl.name, 
                crop.name, 
                zone.name, 
                project.name,
                tag.label,
                tag.color
                `,
        [],
      );
      console.log(sqlConditions);
      const totalElements = totalElementsResult.length || 0;
      // Calculate total pages
      const totalPages = Math.ceil(totalElements / size);
      // Slice the taskList to include only the data for the requested page
      // const startIndex = (page - 1) * size;
      // const endIndex = startIndex + size;
      // const dataForPage = taskList.slice(startIndex, endIndex);
      const formattedResult = taskList.map(item => ({
        ...item,
        task_progress: parseFloat(item.task_progress),
      }));
      // Create pagination metadata
      const pagination = {
        pageNumber: parseInt(page),
        pageSize: parseInt(size),
        totalElements: totalElements,
        totalPages: totalPages,
      };

      // Create the response format
      const response = {
        data: formattedResult,
        pagination: pagination,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  async getTaskManagementInfoTruyxuat(user: ICurrentUser, params: any) {
    try {
      /**
       * verify user
       */
      // Extract page and size parameters from the input
      const page = params.page || 1;
      const size = params.size || 10; // Default page size to 10 if not provided
      const order_by = params.order_by;
      let orderByString = ``;
      if (order_by) {
        orderByString += `ORDER BY ${order_by}`;
      }

      //filter
      let sqlQuery = `
                SELECT 
          task.name, 
          task.label,
          task.tag,
          tag.label as tag_label,
          tag.color as tag_color,
          task.image, 
          task.description, 
          task.assigned_to, 
          task.task_progress, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', iot_user.name, 'first_name', 
                  iot_user.first_name, 'last_name', 
                  iot_user.last_name, 'user_avatar', 
                  iot_user.user_avatar
                )
              ) 
            FROM 
              tabiot_customer_user AS iot_user 
            WHERE 
              iot_user.name = task.assigned_to
          ) AS assigned_to_info, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', involve_in_users.name, 'customer_user', 
                  customer_user, 'task', task, 'first_name', 
                  first_name, 'last_name', last_name
                )
              ) 
            FROM 
              tabiot_assign_user as involve_in_users 
              LEFT JOIN tabiot_customer_user as customer_user ON involve_in_users.customer_user = customer_user.name 
            WHERE 
              involve_in_users.task = task.name
          ) AS involve_in_users, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  worksheet.name, 
                  'label', 
                  worksheet.label, 
                  'description', 
                  worksheet.description, 
                  'quantity', 
                  worksheet.quantity, 
                  'type', 
                  worksheet.type, 
                  'task_id', 
                  worksheet.task_id, 
                  'exp_quantity', 
                  worksheet.exp_quantity,
                  'cost',
                  worksheet.cost, 
                  'work_type_id', 
                  work_type_id, 
                  'work_type', 
                  (
                    SELECT 
                      json_build_object(
                        'name', name, 'label', label, 'customer_id', 
                        customer_id
                      ) 
                    FROM 
                      tabiot_task_work_type as work_type 
                    WHERE 
                      work_type.name = worksheet.work_type_id
                  )
                )
              ) 
            FROM 
              tabiot_farming_plan_task_worksheet AS worksheet 
            WHERE 
              worksheet.task_id = task.name
          ) AS worksheet_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  item_used.name, 
                  'quantity', 
                  item_used.quantity, 
                  'exp_quantity', 
                  item_used.exp_quantity, 
                  'loss_quantity', 
                  item_used.loss_quantity, 
                  'description', 
                  item_used.description, 
                  'task_id', 
                  item_used.task_id, 
                  'iot_category_id', 
                  item_used.iot_category_id, 
                  'category', 
                  (
                    SELECT 
                      json_build_object(
                        'label', 
                        label, 
                        'category_group', 
                        category_group,
                        'image',
                        image,
                        'item_unit_id', 
                        item_unit_id, 
                        'item_unit', 
                        (
                          SELECT 
                            json_build_object(
                              'label', label, 'short_label', short_label, 
                              'customer_id', customer_id
                            ) 
                          FROM 
                            tabiot_item_unit as item_unit 
                          WHERE 
                            item_unit.name = category.item_unit_id
                        )
                      ) 
                    FROM 
                      tabiot_category as category 
                    WHERE 
                      category.name = item_used.iot_category_id
                  )
                )
              ) 
            FROM 
              tabiot_warehouse_item_task_used AS item_used 
            WHERE 
              item_used.task_id = task.name
          ) AS item_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', todo.name, 'label', todo.label, 
                  'status', todo.status, 'description', 
                  todo.description, 'task_id', todo.farming_plan_task, 
                  'image', todo.image, 'start_date', 
                  todo.start_date, 'end_date', todo.end_date, 
                  'is_completed', todo.is_completed,
                  'customer_user_id', todo.customer_user_id,
                  'customer_user_name', CONCAT(cus_user.first_name, ' ', cus_user.last_name)
                )
              ) 
            FROM 
              tabiot_todo AS todo
            LEFT JOIN
              tabiot_customer_user AS cus_user
            ON todo.customer_user_id = cus_user.name
            WHERE 
              todo.farming_plan_task = task.name
          ) AS todo_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  prod_quantity.name, 
                  'quantity', 
                  prod_quantity.quantity, 
                  'exp_quantity', 
                  prod_quantity.exp_quantity,
                  'lost_quantity', 
                  prod_quantity.lost_quantity,  
                  'description', 
                  prod_quantity.description, 
                  'task_id', 
                  prod_quantity.task_id, 
                  'product_id', 
                  prod_quantity.product_id, 
                  'agriculture_product', 
                  (
                    SELECT 
                      json_build_object(
                        'name', name, 'label', label, 'image', 
                        image, 'unit', unit, 'unit_label', 
                        unit_label, 'packing_unit', packing_unit, 
                        'packing_unit_label', packing_unit_label
                      ) 
                    FROM 
                      tabiot_agriculture_product as product 
                    WHERE 
                      product.name = prod_quantity.product_id
                  )
                )
              ) 
            FROM 
              tabiot_production_quantity AS prod_quantity 
            WHERE 
              prod_quantity.task_id = task.name
          ) AS prod_quantity_list, 
          TO_CHAR(task.start_date, 'YYYY-MM-DD HH24:MI:SS') as start_date, 
          TO_CHAR(task.end_date, 'YYYY-MM-DD HH24:MI:SS') as end_date, 
          task.status,
          (
            SELECT json_build_object(
                'label', status.label,
                'color', status.color,
                'customer_id', status.customer_id
            )
            FROM tabiot_task_status AS status
            WHERE status.name = task.status
          ) AS status_detail, 
          pl_state.name AS farming_plan_state, 
          pl_state.label AS state_name, 
          pl.name AS farming_plan, 
          pl.label AS plan_name, 
          crop.name AS crop_id, 
          crop.label AS crop_name, 
          crop.status AS crop_status,
          zone.name AS zone_id, 
          zone.label AS zone_name, 
          project.name AS project_id, 
          project.label AS project_name, 
          project.customer_id AS customer_id, 
          COUNT (todo.*) AS todo_total, 
          COUNT (
            CASE WHEN todo.is_completed = 1 THEN 1 END
          ) AS todo_done 
        FROM 
          tabiot_farming_plan_task AS task 
          LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name 
          LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name 
          LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name 
          LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name 
          LEFT JOIN tabiot_project AS project ON zone.project_id = project.name 
          LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
          LEFT JOIN tabiot_tag AS tag ON tag.name = task.tag
        WHERE

              crop.is_deleted = 0 AND pl.is_deleted = 0
              AND (task.enable_origin_tracing = 1 OR task.added_in_diary = 1)
                  `;

      let filters = params.filters ? JSON.parse(params.filters) : [];
      let sqlConditions: string = '';
      if (filters.length && filters[0].length) {
        sqlConditions += this.otherService.buildTaskManageSQLConditions(filters, 'filters');
      }

      sqlQuery += sqlConditions;
      sqlQuery += `GROUP BY 
                                task.name, 
                                pl_state.name, 
                                pl.name, 
                                crop.name, 
                                zone.name, 
                                project.name,
                                tag.label,
                                tag.color`;
      //pagination
      sqlQuery += `
                ${orderByString}
                OFFSET ${size} * (${page} - 1) LIMIT ${size};
               
                `;
      const taskList = await ERPExecute(sqlQuery, []);
      for (const task of taskList) {
        sortByLabel(task.todo_list);
        sortByLabel(task.worksheet_list);
        sortByLabel(task.prod_quantity_list);
        sortByProperty(task.item_list, 'category');
      }

      const totalElementsResult = await ERPExecute(
        `
                select count(task.name) as count_task from tabiot_farming_plan_task AS task
                LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name
                LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name
                LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name
                LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name
                LEFT JOIN tabiot_project AS project ON zone.project_id = project.name
                LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
                LEFT JOIN tabiot_tag as tag on tag.name = task.tag
              WHERE
                crop.is_deleted = 0 AND pl.is_deleted = 0
                ${sqlConditions}
              GROUP BY 
                task.name, 
                pl_state.name, 
                pl.name, 
                crop.name, 
                zone.name, 
                project.name,
                tag.label,
                tag.color
                `,
        [],
      );
      console.log(sqlConditions);
      const totalElements = totalElementsResult.length || 0;
      // Calculate total pages
      const totalPages = Math.ceil(totalElements / size);
      // Slice the taskList to include only the data for the requested page
      // const startIndex = (page - 1) * size;
      // const endIndex = startIndex + size;
      // const dataForPage = taskList.slice(startIndex, endIndex);
      const formattedResult = taskList.map(item => ({
        ...item,
        task_progress: parseFloat(item.task_progress),
      }));
      // Create pagination metadata
      const pagination = {
        pageNumber: parseInt(page),
        pageSize: parseInt(size),
        totalElements: totalElements,
        totalPages: totalPages,
      };

      // Create the response format
      const response = {
        data: formattedResult,
        pagination: pagination,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  async getTaskManagementInfoToday(user: ICurrentUser, params: any) {
    try {
      if (user.user_type === UserType.SYSTEM_USER) {
        /**
         * verify user
         */
        // Extract page and size parameters from the input
        const page = params.page || 1;
        const size = params.size || 100000; // Default page size to 10 if not provided
        const order_by = params.order_by;
        let orderByString = ``;
        if (order_by) {
          orderByString += `ORDER BY ${order_by}`;
        }
        //check phân quyền participant của vụ mùa
        const cropParticipantList = await cropParticipantsVerify({
          customerId: user.customer_id,
          userId: user.user_id,
          sections: user.sections,
        });
        let cropSQLCondition = 'AND FALSE';
        if (cropParticipantList.length > 0) {
          const cropNames = cropParticipantList.map((d: any) => `'${d.name}'`).join(',');
          cropSQLCondition = `AND crop.name IN (${cropNames})`;
        }
        //filter
        let sqlQuery = `
                SELECT 
          task.name, 
          task.label,
          task.tag,
          tag.label as tag_label,
          tag.color as tag_color,
          task.image, 
          task.description, 
          task.assigned_to, 
          task.task_progress, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', iot_user.name, 'first_name', 
                  iot_user.first_name, 'last_name', 
                  iot_user.last_name, 'user_avatar', 
                  iot_user.user_avatar
                )
              ) 
            FROM 
              tabiot_customer_user AS iot_user 
            WHERE 
              iot_user.name = task.assigned_to
          ) AS assigned_to_info, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', involve_in_users.name, 'customer_user', 
                  customer_user, 'task', task, 'first_name', 
                  first_name, 'last_name', last_name
                )
              ) 
            FROM 
              tabiot_assign_user as involve_in_users 
              LEFT JOIN tabiot_customer_user as customer_user ON involve_in_users.customer_user = customer_user.name 
            WHERE 
              involve_in_users.task = task.name
          ) AS involve_in_users, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  worksheet.name, 
                  'label', 
                  worksheet.label, 
                  'description', 
                  worksheet.description, 
                  'quantity', 
                  worksheet.quantity, 
                  'type', 
                  worksheet.type, 
                  'task_id', 
                  worksheet.task_id, 
                  'exp_quantity', 
                  worksheet.exp_quantity,
                  'cost',
                  worksheet.cost, 
                  'work_type_id', 
                  work_type_id, 
                  'work_type', 
                  (
                    SELECT 
                      json_build_object(
                        'name', name, 'label', label, 'customer_id', 
                        customer_id
                      ) 
                    FROM 
                      tabiot_task_work_type as work_type 
                    WHERE 
                      work_type.name = worksheet.work_type_id
                  )
                )
              ) 
            FROM 
              tabiot_farming_plan_task_worksheet AS worksheet 
            WHERE 
              worksheet.task_id = task.name
          ) AS worksheet_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  item_used.name, 
                  'quantity', 
                  item_used.quantity, 
                  'exp_quantity', 
                  item_used.exp_quantity, 
                  'loss_quantity', 
                  item_used.loss_quantity, 
                  'description', 
                  item_used.description, 
                  'task_id', 
                  item_used.task_id, 
                  'iot_category_id', 
                  item_used.iot_category_id, 
                  'category', 
                  (
                    SELECT 
                      json_build_object(
                        'label', 
                        label, 
                        'category_group', 
                        category_group,
                        'image',
                        image,
                        'item_unit_id', 
                        item_unit_id, 
                        'item_unit', 
                        (
                          SELECT 
                            json_build_object(
                              'label', label, 'short_label', short_label, 
                              'customer_id', customer_id
                            ) 
                          FROM 
                            tabiot_item_unit as item_unit 
                          WHERE 
                            item_unit.name = category.item_unit_id
                        )
                      ) 
                    FROM 
                      tabiot_category as category 
                    WHERE 
                      category.name = item_used.iot_category_id
                  )
                )
              ) 
            FROM 
              tabiot_warehouse_item_task_used AS item_used 
            WHERE 
              item_used.task_id = task.name
          ) AS item_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', todo.name, 'label', todo.label, 
                  'status', todo.status, 'description', 
                  todo.description, 'task_id', todo.farming_plan_task, 
                  'image', todo.image, 'start_date', 
                  todo.start_date, 'end_date', todo.end_date, 
                  'is_completed', todo.is_completed
                )
              ) 
            FROM 
              tabiot_todo AS todo 
            WHERE 
              todo.farming_plan_task = task.name
          ) AS todo_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  prod_quantity.name, 
                  'quantity', 
                  prod_quantity.quantity, 
                  'exp_quantity', 
                  prod_quantity.exp_quantity,
                  'lost_quantity', 
                  prod_quantity.lost_quantity,  
                  'description', 
                  prod_quantity.description, 
                  'task_id', 
                  prod_quantity.task_id, 
                  'product_id', 
                  prod_quantity.product_id, 
                  'agriculture_product', 
                  (
                    SELECT 
                      json_build_object(
                        'name', name, 'label', label, 'image', 
                        image, 'unit', unit, 'unit_label', 
                        unit_label, 'packing_unit', packing_unit, 
                        'packing_unit_label', packing_unit_label
                      ) 
                    FROM 
                      tabiot_agriculture_product as product 
                    WHERE 
                      product.name = prod_quantity.product_id
                  )
                )
              ) 
            FROM 
              tabiot_production_quantity AS prod_quantity 
            WHERE 
              prod_quantity.task_id = task.name
          ) AS prod_quantity_list, 
          TO_CHAR(task.start_date, 'YYYY-MM-DD HH24:MI:SS') as start_date, 
          TO_CHAR(task.end_date, 'YYYY-MM-DD HH24:MI:SS') as end_date, 
          task.status,
          (
            SELECT json_build_object(
                'label', status.label,
                'color', status.color,
                'customer_id', status.customer_id
            )
            FROM tabiot_task_status AS status
            WHERE status.name = task.status
          ) AS status_detail, 
          pl_state.name AS farming_plan_state, 
          pl_state.label AS state_name, 
          pl.name AS farming_plan, 
          pl.label AS plan_name, 
          crop.name AS crop_id, 
          crop.label AS crop_name, 
          crop.status AS crop_status,
          zone.name AS zone_id, 
          zone.label AS zone_name, 
          project.name AS project_id, 
          project.label AS project_name, 
          project.customer_id AS customer_id, 
          COUNT (todo.*) AS todo_total, 
          COUNT (
            CASE WHEN todo.is_completed = 1 THEN 1 END
          ) AS todo_done 
        FROM 
          tabiot_farming_plan_task AS task 
          LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name 
          LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name 
          LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name 
          LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name 
          LEFT JOIN tabiot_project AS project ON zone.project_id = project.name 
          LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
          LEFT JOIN tabiot_tag AS tag ON tag.name = task.tag
        WHERE

                crop.is_deleted = 0 AND pl.is_deleted = 0
                AND DATE(task.start_date) <= CURRENT_DATE 
                AND DATE(task.end_date) >= CURRENT_DATE
                ${cropSQLCondition}
                  `;

        let filters = params.filters ? JSON.parse(params.filters) : [];
        let sqlConditions: string = '';
        if (filters.length && filters[0].length) {
          sqlConditions += this.otherService.buildTaskManageSQLConditions(filters, 'filters');
        }

        sqlQuery += sqlConditions;
        sqlQuery += `GROUP BY 
                                task.name, 
                                pl_state.name, 
                                pl.name, 
                                crop.name, 
                                zone.name, 
                                project.name,
                                tag.label,
                                tag.color`;
        //pagination
        sqlQuery += `
                ${orderByString}
                OFFSET ${size} * (${page} - 1) LIMIT ${size};
               
                `;
        const taskList = await ERPExecute(sqlQuery, []);
        for (const task of taskList) {
          sortByLabel(task.todo_list);
          sortByLabel(task.worksheet_list);
          sortByLabel(task.prod_quantity_list);
          sortByProperty(task.item_list, 'category');
        }

        const totalElementsResult = await ERPExecute(
          `
                select count(task.name) as count_task from tabiot_farming_plan_task AS task
                LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name
                LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name
                LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name
                LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name
                LEFT JOIN tabiot_project AS project ON zone.project_id = project.name
                LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
                LEFT JOIN tabiot_tag as tag on tag.name = task.tag
              WHERE
                crop.is_deleted = 0 AND pl.is_deleted = 0
                AND DATE(task.start_date) <= CURRENT_DATE 
                AND DATE(task.end_date) >= CURRENT_DATE
                ${cropSQLCondition}
                ${sqlConditions}
              GROUP BY 
                task.name, 
                pl_state.name, 
                pl.name, 
                crop.name, 
                zone.name, 
                project.name,
                tag.label,
                tag.color
                `,
          [],
        );
        const totalElements = totalElementsResult.length || 0;
        // Calculate total pages
        // Slice the taskList to include only the data for the requested page
        // const startIndex = (page - 1) * size;
        // const endIndex = startIndex + size;
        // const dataForPage = taskList.slice(startIndex, endIndex);
        const formattedResult = taskList.map(item => ({
          ...item,
          task_progress: parseFloat(item.task_progress),
        }));
        //filter formattedResult to just return today task (just compare date in start_date and end_date fields, these field have format "2023-12-08T10:00:00.000Z" dont compare time)
        // const todayTask = formattedResult.filter(item => {
        //   const itemStartDate = new Date(item.start_date);
        //   const itemEndDate = new Date(item.end_date);

        //   const today = new Date();
        //   const dateOnly = (date: any) => new Date(date.toISOString().split('T')[0]);
        //   const condition = dateOnly(itemStartDate) <= dateOnly(today) && dateOnly(today) <= dateOnly(itemEndDate);
        //   return condition;
        //   // return (
        //   //   (itemStartDate.getFullYear() <= today.getFullYear() &&
        //   //     itemStartDate.getMonth() <= today.getMonth() &&
        //   //     itemStartDate.getDate() <= today.getDate()) ||
        //   //   (itemEndDate.getFullYear() >= today.getFullYear() &&
        //   //     itemEndDate.getMonth() >= today.getMonth() &&
        //   //     itemEndDate.getDate() >= today.getDate())
        //   // );
        // });
        // Create pagination metadata
        const totalPages = Math.ceil(totalElements / size);

        const pagination = {
          pageNumber: parseInt(page),
          pageSize: parseInt(size),
          totalElements: totalElements,
          totalPages: totalPages,
        };

        // Create the response format
        const response = {
          data: formattedResult,
          pagination: pagination,
        };
        return response;
      } else {
        /**
         * verify user
         */
        // Extract page and size parameters from the input
        const page = params.page || 1;
        const size = params.size || 100000; // Default page size to 10 if not provided
        const order_by = params.order_by;
        let orderByString = ``;
        if (order_by) {
          orderByString += `ORDER BY ${order_by}`;
        }
        //filter
        let sqlQuery = `
                SELECT 
          task.name, 
          task.label,
          task.tag,
          tag.label as tag_label,
          tag.color as tag_color,
          task.image, 
          task.description, 
          task.assigned_to, 
          task.task_progress, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', iot_user.name, 'first_name', 
                  iot_user.first_name, 'last_name', 
                  iot_user.last_name, 'user_avatar', 
                  iot_user.user_avatar
                )
              ) 
            FROM 
              tabiot_customer_user AS iot_user 
            WHERE 
              iot_user.name = task.assigned_to
          ) AS assigned_to_info, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', involve_in_users.name, 'customer_user', 
                  customer_user, 'task', task, 'first_name', 
                  first_name, 'last_name', last_name
                )
              ) 
            FROM 
              tabiot_assign_user as involve_in_users 
              LEFT JOIN tabiot_customer_user as customer_user ON involve_in_users.customer_user = customer_user.name 
            WHERE 
              involve_in_users.task = task.name
          ) AS involve_in_users, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  worksheet.name, 
                  'label', 
                  worksheet.label, 
                  'description', 
                  worksheet.description, 
                  'quantity', 
                  worksheet.quantity, 
                  'type', 
                  worksheet.type, 
                  'task_id', 
                  worksheet.task_id, 
                  'exp_quantity', 
                  worksheet.exp_quantity,
                  'cost',
                  worksheet.cost, 
                  'work_type_id', 
                  work_type_id, 
                  'work_type', 
                  (
                    SELECT 
                      json_build_object(
                        'name', name, 'label', label, 'customer_id', 
                        customer_id
                      ) 
                    FROM 
                      tabiot_task_work_type as work_type 
                    WHERE 
                      work_type.name = worksheet.work_type_id
                  )
                )
              ) 
            FROM 
              tabiot_farming_plan_task_worksheet AS worksheet 
            WHERE 
              worksheet.task_id = task.name
          ) AS worksheet_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  item_used.name, 
                  'quantity', 
                  item_used.quantity, 
                  'exp_quantity', 
                  item_used.exp_quantity, 
                  'loss_quantity', 
                  item_used.loss_quantity, 
                  'description', 
                  item_used.description, 
                  'task_id', 
                  item_used.task_id, 
                  'iot_category_id', 
                  item_used.iot_category_id, 
                  'category', 
                  (
                    SELECT 
                      json_build_object(
                        'label', 
                        label, 
                        'category_group', 
                        category_group,
                        'image',
                        image,
                        'item_unit_id', 
                        item_unit_id, 
                        'item_unit', 
                        (
                          SELECT 
                            json_build_object(
                              'label', label, 'short_label', short_label, 
                              'customer_id', customer_id
                            ) 
                          FROM 
                            tabiot_item_unit as item_unit 
                          WHERE 
                            item_unit.name = category.item_unit_id
                        )
                      ) 
                    FROM 
                      tabiot_category as category 
                    WHERE 
                      category.name = item_used.iot_category_id
                  )
                )
              ) 
            FROM 
              tabiot_warehouse_item_task_used AS item_used 
            WHERE 
              item_used.task_id = task.name
          ) AS item_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', todo.name, 'label', todo.label, 
                  'status', todo.status, 'description', 
                  todo.description, 'task_id', todo.farming_plan_task, 
                  'image', todo.image, 'start_date', 
                  todo.start_date, 'end_date', todo.end_date, 
                  'is_completed', todo.is_completed,
                  'customer_user_id', todo.customer_user_id,
                  'customer_user_name', CONCAT(cus_user.first_name, ' ', cus_user.last_name)
                )
              ) 
            FROM 
              tabiot_todo AS todo
            LEFT JOIN
              tabiot_customer_user AS cus_user
            ON todo.customer_user_id = cus_user.name
            WHERE 
              todo.farming_plan_task = task.name
          ) AS todo_list, 
          (
            SELECT 
              json_agg(
                json_build_object(
                  'name', 
                  prod_quantity.name, 
                  'quantity', 
                  prod_quantity.quantity, 
                  'exp_quantity', 
                  prod_quantity.exp_quantity,
                  'lost_quantity', 
                  prod_quantity.lost_quantity,  
                  'description', 
                  prod_quantity.description, 
                  'task_id', 
                  prod_quantity.task_id, 
                  'product_id', 
                  prod_quantity.product_id, 
                  'agriculture_product', 
                  (
                    SELECT 
                      json_build_object(
                        'name', name, 'label', label, 'image', 
                        image, 'unit', unit, 'unit_label', 
                        unit_label, 'packing_unit', packing_unit, 
                        'packing_unit_label', packing_unit_label
                      ) 
                    FROM 
                      tabiot_agriculture_product as product 
                    WHERE 
                      product.name = prod_quantity.product_id
                  )
                )
              ) 
            FROM 
              tabiot_production_quantity AS prod_quantity 
            WHERE 
              prod_quantity.task_id = task.name
          ) AS prod_quantity_list, 
          TO_CHAR(task.start_date, 'YYYY-MM-DD HH24:MI:SS') as start_date, 
          TO_CHAR(task.end_date, 'YYYY-MM-DD HH24:MI:SS') as end_date, 
          task.status,
          (
            SELECT json_build_object(
                'label', status.label,
                'color', status.color,
                'customer_id', status.customer_id
            )
            FROM tabiot_task_status AS status
            WHERE status.name = task.status
          ) AS status_detail, 
          pl_state.name AS farming_plan_state, 
          pl_state.label AS state_name, 
          pl.name AS farming_plan, 
          pl.label AS plan_name, 
          crop.name AS crop_id, 
          crop.label AS crop_name, 
          crop.status AS crop_status,
          zone.name AS zone_id, 
          zone.label AS zone_name, 
          project.name AS project_id, 
          project.label AS project_name, 
          project.customer_id AS customer_id, 
          COUNT (todo.*) AS todo_total, 
          COUNT (
            CASE WHEN todo.is_completed = 1 THEN 1 END
          ) AS todo_done 
        FROM 
          tabiot_farming_plan_task AS task 
          LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name 
          LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name 
          LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name 
          LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name 
          LEFT JOIN tabiot_project AS project ON zone.project_id = project.name 
          LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
          LEFT JOIN tabiot_tag AS tag ON tag.name = task.tag
        WHERE

                project.customer_id = '${user.customer_id}'
                AND crop.is_deleted = 0 AND pl.is_deleted = 0
                AND DATE(task.start_date) <= CURRENT_DATE 
                AND DATE(task.end_date) >= CURRENT_DATE
                  `;

        let filters = params.filters ? JSON.parse(params.filters) : [];
        let sqlConditions: string = '';
        if (filters.length && filters[0].length) {
          sqlConditions += this.otherService.buildTaskManageSQLConditions(filters, 'filters');
        }

        sqlQuery += sqlConditions;
        sqlQuery += `GROUP BY 
                                task.name, 
                                pl_state.name, 
                                pl.name, 
                                crop.name, 
                                zone.name, 
                                project.name,
                                tag.label,
                                tag.color`;
        //pagination
        sqlQuery += `
                ${orderByString}
                OFFSET ${size} * (${page} - 1) LIMIT ${size};
               
                `;
        const taskList = await ERPExecute(sqlQuery, []);
        for (const task of taskList) {
          sortByLabel(task.todo_list);
          sortByLabel(task.worksheet_list);
          sortByLabel(task.prod_quantity_list);
          sortByProperty(task.item_list, 'category');
        }

        const totalElementsResult = await ERPExecute(
          `
                select count(task.name) as count_task from tabiot_farming_plan_task AS task
                LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name
                LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name
                LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name
                LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name
                LEFT JOIN tabiot_project AS project ON zone.project_id = project.name
                LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
                LEFT JOIN tabiot_tag as tag on tag.name = task.tag
              WHERE
                project.customer_id = '${user.customer_id}'
                AND crop.is_deleted = 0 AND pl.is_deleted = 0
                AND DATE(task.start_date) <= CURRENT_DATE 
                AND DATE(task.end_date) >= CURRENT_DATE
                ${sqlConditions}
              GROUP BY 
                task.name, 
                pl_state.name, 
                pl.name, 
                crop.name, 
                zone.name, 
                project.name,
                tag.label,
                tag.color
                `,
          [],
        );
        console.log(sqlConditions);
        const totalElements = totalElementsResult.length || 0;
        const formattedResult = taskList.map(item => ({
          ...item,
          task_progress: parseFloat(item.task_progress),
        }));
        // const todayTask = formattedResult.filter(item => {
        //   const itemStartDate = new Date(item.start_date);
        //   const itemEndDate = new Date(item.end_date);

        //   const today = new Date();
        //   const dateOnly = (date: any) => new Date(date.toISOString().split('T')[0]);
        //   const condition = dateOnly(itemStartDate) <= dateOnly(today) && dateOnly(today) <= dateOnly(itemEndDate);
        //   return condition;
        //   // return (
        //   //   (itemStartDate.getFullYear() <= today.getFullYear() &&
        //   //     itemStartDate.getMonth() <= today.getMonth() &&
        //   //     itemStartDate.getDate() <= today.getDate()) ||
        //   //   (itemEndDate.getFullYear() >= today.getFullYear() &&
        //   //     itemEndDate.getMonth() >= today.getMonth() &&
        //   //     itemEndDate.getDate() >= today.getDate())
        //   // );
        // });
        // Create pagination metadata
        const totalPages = Math.ceil(formattedResult.length / size);
        // Create pagination metadata
        const pagination = {
          pageNumber: parseInt(page),
          pageSize: parseInt(size),
          totalElements: totalElements,
          totalPages: totalPages,
        };

        // Create the response format
        const response = {
          data: formattedResult,
          pagination: pagination,
        };
        return response;
      }
    } catch (error) {
      throw error;
    }
  }

  async getTaskManagementInfoTracing(user: ICurrentUser, params: any) {
    try {
      if (user.user_type === UserType.SYSTEM_USER) {
        const page = params.page || 1;
        const size = params.size || 10; // Default page size to 10 if not provided
        let taskList = await ERPExecute(
          `
                    SELECT  
                        task.name,
                        task.label,
                        task.enable_origin_tracing,
                        task.image, task.description, task.assigned_to,
                        (
                            SELECT json_agg(json_build_object(
                                'user_id', iot_user.name,
                                'first_name', iot_user.first_name,
                                'last_name', iot_user.last_name,
                                'user_avatar', iot_user.user_avatar 
                            ))
                            FROM tabiot_customer_user AS iot_user
                            WHERE iot_user.name = task.assigned_to
                        ) AS assigned_to_info,
                        task.involved_in,
                        task.start_date,
                        task.end_date,
                        task.status,
                        pl_state.name AS pl_state_id,
                        pl_state.label AS pl_state_name,
                        pl.name AS pl_id,
                        pl.label AS pl_name,
                        crop.name AS crop_id,
                        crop.label AS crop_name,
                        zone.name AS zone_id,
                        zone.label AS zone_name,
                        project.name AS project_id,
                        project.label AS project_name,
                        project.customer_id AS customer_id,
                        COUNT (todo.*) AS todo_total,
                        COUNT (CASE WHEN todo.status='Done' THEN 1 END) AS todo_done
                    FROM tabiot_farming_plan_task AS task
                    LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name
                    LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name
                    LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name
                    LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name
                    LEFT JOIN tabiot_project AS project ON zone.project_id = project.name
                    LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
                    WHERE task.enable_origin_tracing = 1
                    GROUP BY task.name, pl_state.name, pl.name, crop.name, zone.name, project.name
                    `,
          [],
        );

        let involvedList: any = [];
        taskList?.map((task: any) => {
          if (!task.assigned_to_info) {
            task.assigned_to_info = [];
          }
          let involvedStr = task.involved_in;
          if (involvedStr) {
            involvedStr = involvedStr.replace(/[[\]]/g, ''); // Remove the square brackets
            var array = involvedStr.split(',').map((item: any) => item.trim());
            array.map((involved: any) => {
              involvedList.push(involved);
            });
          }
        });

        if (involvedList.length) {
          let query = `
                    SELECT
                        name AS user_id,
                        first_name,
                        last_name,
                        user_avatar 
                    FROM tabiot_customer_user 
                    WHERE `;
          involvedList.map((involved: any) => {
            query += `name = '${involved}' OR `;
          });
          query = query.slice(0, -3);

          let involvedInforList = await ERPExecute(query, []);

          taskList?.map((task: any) => {
            task.involved_in_info = [];
            let involvedStr = task.involved_in;
            if (involvedStr) {
              involvedStr = involvedStr.replace(/[[\]]/g, ''); // Remove the square brackets
              var array = involvedStr.split(',').map((item: any) => item.trim());
              array.map((involved: any) => {
                involvedInforList.map((involvedInfo: any) => {
                  if (involved == involvedInfo.user_id) {
                    task.involved_in_info.push(involvedInfo);
                  }
                });
              });
            }
          });
        }
        const totalElementsResult = taskList;
        const totalElements = totalElementsResult.length || 0;
        console.log(totalElements, 'totel');
        // Calculate total pages
        const totalPages = Math.ceil(totalElements / size);
        // Slice the taskList to include only the data for the requested page
        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;
        const dataForPage = taskList.slice(startIndex, endIndex);
        // Create pagination metadata
        const pagination = {
          pageNumber: page,
          pageSize: size,
          totalElements: totalElements,
          totalPages: totalPages,
          // Add any order_by information if needed
        };

        // Create the response format
        const response = {
          data: dataForPage,
          pagination: pagination,
        };
        return response;
      } else {
        /**
         * verify user
         */
        // Extract page and size parameters from the input
        const page = params.page || 1;
        const size = params.size || 10; // Default page size to 10 if not provided

        //filter
        let sqlQuery = `
                SELECT
                task.name,
                task.enable_origin_tracing,
                task.label,
                task.image,
                task.description,
                task.assigned_to,
                task.task_progress,
                (
                  SELECT
                    json_agg(
                      json_build_object(
                        'name', iot_user.name, 'first_name',
                        iot_user.first_name, 'last_name',
                        iot_user.last_name, 'user_avatar',
                        iot_user.user_avatar
                      )
                    )
                  FROM
                    tabiot_customer_user AS iot_user
                  WHERE
                    iot_user.name = task.assigned_to
                ) AS assigned_to_info,
                (
                  SELECT
                    json_agg(
                      json_build_object(
                        'name', involve_in_users.name,
                        'customer_user', customer_user, 'task',
                        task, 'first_name', first_name, 'last_name',
                        last_name
                      )
                    )
                  FROM
                    tabiot_assign_user as involve_in_users
                    LEFT JOIN tabiot_customer_user as customer_user ON involve_in_users.customer_user = customer_user.name
                  WHERE
                    involve_in_users.task = task.name
                ) AS involve_in_users,
                (
                  SELECT 
                    json_agg(
                      json_build_object(
                        'name', 
                        worksheet.name, 
                        'label', 
                        worksheet.label, 
                        'description', 
                        worksheet.description, 
                        'quantity', 
                        worksheet.quantity, 
                        'type', 
                        worksheet.type, 
                        'task_id', 
                        worksheet.task_id, 
                        'exp_quantity', 
                        worksheet.exp_quantity, 
                        'work_type_id', 
                        work_type_id, 
                        'work_type', 
                        (
                          SELECT 
                            json_build_object(
                              'label', label, 'customer_id', customer_id
                            ) 
                          FROM 
                            tabiot_task_work_type as work_type 
                          WHERE 
                            work_type.name = worksheet.work_type_id
                        )
                      )
                    ) 
                  FROM 
                    tabiot_farming_plan_task_worksheet AS worksheet 
                  WHERE 
                    worksheet.task_id = task.name
                ) AS worksheet_list, 
                (
                  SELECT
                    json_agg(
                      json_build_object(
                        'name', item_used.name, 
                        'quantity', item_used.quantity, 
                        'exp_quantity', item_used.exp_quantity,
                        'description', item_used.description, 
                        'task_id', item_used.task_id, 
                        'iot_category_id', 
                        item_used.iot_category_id, 
                        'category',
                        (SELECT
                          json_build_object(
                            'label', label,
                            'category_group', category_group,
                            'item_unit_id', item_unit_id,
                            'item_unit',
                            (
                              SELECT
                                json_buile_object(
                                  'label', label,
                                  'short_label', short_label,
                                  'customer_id', customer_id
                                )
                              FROM
                                tabiot_item_unit as item_unit
                              WHERE
                              item_unit.name = category.item_unit_id
                            )
                        )
                        FROM
                          tabiot_category as category
                        WHERE
                          category.name = item_used.iot_category_id)
                      )
                    )
                  FROM
                    tabiot_warehouse_item_task_used AS item_used
                  WHERE
                    item_used.task_id = task.name
                ) AS item_list,
                (
                    SELECT
                      json_agg(
                        json_build_object(
                          'name', todo.name,
                          'label', todo.label,
                           'status',
                          todo.status, 'description',
                          todo.description, 'task_id',
                          todo.farming_plan_task, 'image',
                          todo.image, 'start_date',
                          todo.start_date,
                          'end_date', todo.end_date,
                          'is_completed', todo.is_completed
                        )
                      )
                    FROM
                      tabiot_todo AS todo
                    WHERE
                      todo.farming_plan_task = task.name
                  ) AS todo_list,
                (
                  SELECT
                    json_agg(
                      json_build_object(
                        'name',
                        prod_quantity.name,
                        'quantity',
                        prod_quantity.quantity,
                        'exp_quantity', 
                        prod_quantity.exp_quantity, 
                        'description',
                        prod_quantity.description,
                        'task_id',
                        prod_quantity.task_id,
                        'product_id',
                        prod_quantity.product_id,
                        'agriculture_product',
                        (SELECT
                          json_build_object(
                            'label', label,
                            'image', image,
                            'unit', unit,
                            'unit_label', unit_label,
                            'packing_unit', packing_unit,
                            'packing_unit_label', packing_unit_label
                        )
                        FROM
                          tabiot_agriculture_product as product
                        WHERE
                          product.name = prod_quantity.product_id)
                      )
                    )
                  FROM
                    tabiot_production_quantity AS prod_quantity
                  WHERE
                    prod_quantity.task_id = task.name
                ) AS prod_quantity_list,
                task.start_date,
                task.end_date,
                task.status,
                pl_state.name AS farming_plan_state,
                pl_state.label AS state_name,
                pl.name AS farming_plan,
                pl.label AS plan_name,
                crop.name AS crop_id,
                crop.label AS crop_name,
                zone.name AS zone_id,
                zone.label AS zone_name,
                project.name AS project_id,
                project.label AS project_name,
                project.customer_id AS customer_id,
                COUNT (todo.*) AS todo_total,
                COUNT (
                  CASE WHEN todo.is_completed = 1 THEN 1 END
                ) AS todo_done
              FROM
                tabiot_farming_plan_task AS task
                LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name
                LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name
                LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name
                LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name
                LEFT JOIN tabiot_project AS project ON zone.project_id = project.name
                LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
              WHERE
                project.customer_id = '${user.customer_id}' AND task.enable_origin_tracing = 1
                  `;

        let filters = params.filters ? JSON.parse(params.filters) : [];
        let sqlConditions: string = '';
        if (filters.length && filters[0].length) {
          sqlConditions += this.otherService.buildTaskManageSQLConditions(filters, 'filters');
        }

        sqlQuery += sqlConditions;
        sqlQuery += `GROUP BY 
                                task.name, 
                                pl_state.name, 
                                pl.name, 
                                crop.name, 
                                zone.name, 
                                project.name`;
        //pagination
        sqlQuery += `
                OFFSET ${size} * (${page} - 1) LIMIT ${size}
                `;
        const taskList = await ERPExecute(sqlQuery, []);
        const totalElementsResult = await ERPExecute(
          `
                select count(task.name) as count_task from tabiot_farming_plan_task AS task
                LEFT JOIN tabiot_farming_plan_state AS pl_state ON task.farming_plan_state = pl_state.name
                LEFT JOIN tabiot_farming_plan AS pl ON pl_state.farming_plan = pl.name
                LEFT JOIN tabiot_crop AS crop ON pl.crop = crop.name
                LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name
                LEFT JOIN tabiot_project AS project ON zone.project_id = project.name
                LEFT JOIN tabiot_todo AS todo ON todo.farming_plan_task = task.name
              WHERE
                project.customer_id = '${user.customer_id}'`,
          [],
        );

        const totalElements = totalElementsResult[0].count_task || 0;
        console.log(totalElements, 'totel');
        // Calculate total pages
        const totalPages = Math.ceil(totalElements / size);
        // Slice the taskList to include only the data for the requested page
        // const startIndex = (page - 1) * size;
        // const endIndex = startIndex + size;
        // const dataForPage = taskList.slice(startIndex, endIndex);
        // Create pagination metadata
        const pagination = {
          pageNumber: parseInt(page),
          pageSize: parseInt(size),
          totalElements: parseInt(totalElements),
          totalPages: totalPages,
        };

        // Create the response format
        const response = {
          data: taskList,
          pagination: pagination,
        };
        return response;
      }
    } catch (error) {
      throw error;
    }
  }

  async createTaskAdmin(user: ICurrentUser, body: IIotFarmingPlan) {
    const response = await this.frappeService.generalCreate({
      doc_name: 'iot_farming_plan_task',
      data: body,
    });
    return response;
  }

  async createTask(user: ICurrentUser, body: IIotFarmingPlanTask) {
    const cropList = await this.otherService.getCropList(user);
    const crops = cropList.map(item => `'${item.name}'`).join(', ');
    const stateList = await ERPExecute(
      `
            SELECT state.name
            FROM "tabiot_farming_plan_state" as state
            INNER JOIN
                "tabiot_farming_plan" as plan
            ON
                state.farming_plan = plan.name
            WHERE plan.crop IN (${crops})
            `,
      [],
    );
    const verifyState = stateList.some(obj => obj.name === body.farming_plan_state);
    if (!verifyState) {
      throw new HttpError(403, 'You cannot create this biot_Farming plan - Task');
    }
    const response = await this.frappeService.generalCreate({
      doc_name: 'iot_farming_plan_task',
      data: body,
    });
    return response;
  }

  async updateTaskAdmin(user: ICurrentUser, body: IIotFarmingPlanTask) {
    const response = await this.frappeService.generalUpdate({
      doc_name: 'iot_farming_plan_task',
      name: body.name.toString(),
      data: body,
    });
    return response;
  }

  async updateTask(user: ICurrentUser, body: IIotFarmingPlanTask) {
    try {
      const cropList = await this.otherService.getCropList(user);
      const crops = cropList.map(item => `'${item.name}'`).join(', ');
      const verifyTask = await ERPExecute(
        `
                SELECT task.name as task_name, state.name as state_name FROM "tabiot_farming_plan_task" as task
                INNER JOIN
                    "tabiot_farming_plan_state" as state
                ON
                    task.farming_plan_state = state.name
                INNER JOIN
                    "tabiot_farming_plan" as plan
                ON
                    state.farming_plan = plan.name
                WHERE plan.crop IN (${crops})
                `,
        [],
      );
      const verifyState = await ERPExecute(
        `
                SELECT state.name as state_name
                FROM 
                    "tabiot_farming_plan_state" as state
                INNER JOIN
                    "tabiot_farming_plan" as plan
                ON
                    state.farming_plan = plan.name
                WHERE plan.crop IN (${crops})
                `,
        [],
      );

      const condition: any =
        verifyTask.some(obj => obj.task_name === body.name) &&
        verifyState.some(obj => obj.state_name === body.farming_plan_state);
      if (condition) {
        const response: any = await this.frappeService.generalUpdate({
          doc_name: 'iot_farming_plan_task',
          name: body.name.toString(),
          data: body,
        });
        //insert on duplicate update employee in crop
        const crop_id = await getCropFromTaskName(body.name.toString());
        const bodyEmployeeInCrop: IIotEmployeeInCrop = {
          iot_crop: crop_id,
          iot_customer_user: body.assigned_to,
          name: `${crop_id}-${body.assigned_to}`,
        };
        await this.employeeInCropService.upsertEmployeeInCrop(user, bodyEmployeeInCrop);
        //notification to assigned user
        const assigned_to = body.assigned_to;
        //check xem đã có tạo notification trong hệ thống chưa
        const existNoti = await ERPExecute(
          `
        SELECT
          name
        FROM
          tabiot_notification
        WHERE
          entity = '${body.name}'
          AND message = 'Bạn vừa được giao công việc: ${body.label}'
        `,
          [],
        );
        if (existNoti.length > 0) {
          return response;
        }
        //create notification for user
        const notiObject = {
          customer_user: assigned_to,
          message: `Bạn vừa được giao công việc: ${body.label}`,
          created_at: moment().add(7, 'hours').format('YYYY-MM-DD HH:mm:ss'),
          entity: response.data.name,
          type: 'task',
          is_read: 0,
        };
        const noti: any = await this.frappeService.generalCreate({
          doc_name: 'iot_notification',
          data: notiObject,
        });
        //push notification to mobile
        const pushNotiBody = {
          topic: `arn:aws:sns:ap-southeast-1:434827713262:${assigned_to}`,
          title: 'Thông báo công việc',
          body: `Bạn vừa được giao công việc: ${body.label}`,
          noti_id: noti.data.name,
          entity: response.data.name ? response.data.name : '',
          type: 'Alarm',
        };
        await this.notificationService.publishMobileNotificationToTopicAdmin(pushNotiBody);
        return response;
      }

      throw new HttpError(
        403,
        JSON.stringify({
          status: 403,
          message: "You don't have permission to update this iot_farming_plan task",
        }),
      );
    } catch (error) {
      console.log('error', error);
      throw error;
    }
  }

  async updateTaskProgress(user: ICurrentUser, params: { task_id: string }) {
    try {
      const todoList = await ERPExecute(
        `
        SELECT todo.name, todo.label, todo.is_completed
        FROM tabiot_todo as todo
        INNER JOIN tabiot_farming_plan_task as task
        ON
          task.name = todo.farming_plan_task
        WHERE
          task.name = $1
      `,
        [params.task_id],
      );
      const completedCount = todoList.filter(item => item.is_completed === 1).length;
      const totalCount = todoList.length;
      const percentCompleted = completedCount / totalCount;

      const updateTaskProgess = await ERPExecute(
        `
      UPDATE tabiot_farming_plan_task
      SET task_progress = $1
      WHERE name = $2
    `,
        [percentCompleted, params.task_id],
      );
      return updateTaskProgess;
    } catch (error) {
      throw error;
    }
  }

  async deleteTaskAdmin(user: ICurrentUser, params: { name: string }) {
    const deleteRc: any = await this.frappeService.generalDelete({
      name: params.name,
      doc_name: 'iot_farming_plan_task',
    });
    return deleteRc;
  }
  async deleteTask(user: ICurrentUser, params: { name: string }) {
    try {
      const cropList = await this.otherService.getCropList(user);
      const crops = cropList.map(item => `'${item.name}'`).join(', ');
      const verifyTask = await ERPExecute(
        `
                SELECT task.name as task_name, state.name as state_name FROM "tabiot_farming_plan_task" as task
                INNER JOIN
                    "tabiot_farming_plan_state" as state
                ON
                    task.farming_plan_state = state.name
                INNER JOIN
                    "tabiot_farming_plan" as plan
                ON
                    state.farming_plan = plan.name
                WHERE plan.crop IN (${crops})
                `,
        [],
      );

      const condition: any = verifyTask.some(obj => obj.task_name === params.name);
      if (condition) {
        const deleteRc: any = await this.frappeService.generalDelete({
          name: params.name,
          doc_name: 'iot_farming_plan_task',
        });
        return deleteRc;
      }
      throw new HttpError(
        403,
        JSON.stringify({
          status: 403,
          message: "You don't have permission to delete this iot_farming_plan state",
        }),
      );
    } catch (error) {
      throw error;
    }
  }

  async deleteTaskAllResource(user: ICurrentUser, params: { name: string }) {
    try {
      const cropList = await this.otherService.getCropList(user);
      const crops = cropList.map(item => `'${item.name}'`).join(', ');
      const verifyTask = await ERPExecute(
        `
                SELECT task.name as task_name, state.name as state_name
                FROM "tabiot_farming_plan_task" as task
                INNER JOIN
                    "tabiot_farming_plan_state" as state
                ON
                    task.farming_plan_state = state.name
                INNER JOIN
                    "tabiot_farming_plan" as plan
                ON
                    state.farming_plan = plan.name
                WHERE plan.crop IN (${crops})
                `,
        [],
      );

      const condition: any = verifyTask.some(obj => obj.task_name === params.name);
      if (!condition) {
        throw new HttpError(
          403,
          JSON.stringify({
            status: 403,
            message: "You don't have permission to delete this iot_farming_plan task",
          }),
        );
      }

      // IMPORTANT: Clean up yield allocations BEFORE deleting the task
      // This prevents orphaned allocations and ensures remaining_quantity is properly updated
      await this.cleanupTaskYieldAllocations(user, params.name);

      //start delete task's stuffs [[query: "", params: []], [query: "", params: []]]
      //iot_todo
      const deleteiot_todoSQL: string = `
            DELETE FROM tabiot_todo WHERE farming_plan_task = '${params.name}'
            `;
      //iot_assign_user
      const deleteiot_assign_userSQL: string = `
            DELETE FROM tabiot_assign_user WHERE task = '${params.name}'
            `;
      //iot_warehouse_item_task_used
      const deleteiot_warehouse_item_task_usedSQL: string = `
            DELETE FROM tabiot_warehouse_item_task_used WHERE task_id = '${params.name}'
            `;
      //iot_farming_plan_task_worksheet
      const deleteiot_farming_plan_task_worksheetSQL: string = `
            DELETE FROM tabiot_farming_plan_task_worksheet WHERE task_id = '${params.name}'
            `;
      //iot_production_quantity
      const deleteiot_production_quantitySQL: string = `
            DELETE FROM tabiot_production_quantity WHERE task_id = '${params.name}'
            `;
      // //delete task_item_transfer records (new structure)
      // const deleteTaskItemTransferSQL: string = `
      //       DELETE FROM task_item_transfer WHERE source_task_id = '${params.name}' OR target_task_id = '${params.name}'
      //       `;
      //delete iot_farming_plan_task
      const deleteiot_farming_plan_taskSQL: string = `
            DELETE FROM tabiot_farming_plan_task WHERE name = '${params.name}'
            `;
      const queryArr = [
        deleteiot_todoSQL,
        deleteiot_assign_userSQL,
        deleteiot_warehouse_item_task_usedSQL,
        deleteiot_farming_plan_task_worksheetSQL,
        deleteiot_production_quantitySQL,
        // deleteTaskItemTransferSQL,
        deleteiot_farming_plan_taskSQL,
      ];
      const deleteAll = await ERPExecuteTransaction(queryArr, []);
      return deleteAll;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Bulk delete multiple tasks with proper yield allocation cleanup
   * This method processes multiple tasks efficiently while maintaining data consistency
   */
  async deleteTasksBulk(user: ICurrentUser, taskIds: string[]): Promise<{
    success: boolean;
    deletedTasks: string[];
    failedTasks: { taskId: string; error: string }[];
    summary: {
      totalRequested: number;
      successfullyDeleted: number;
      failed: number;
      allocationsCleanedUp: number;
    };
  }> {
    try {
      if (!taskIds || taskIds.length === 0) {
        throw new HttpError(400, 'No task IDs provided for bulk deletion');
      }

      console.log(`Starting bulk deletion of ${taskIds.length} tasks`);

      // Validate all tasks belong to user's crops first
      const cropList = await this.otherService.getCropList(user);
      const crops = cropList.map(item => `'${item.name}'`).join(', ');

      const verifyTasks = await ERPExecute(
        `
        SELECT task.name as task_name, state.name as state_name
        FROM "tabiot_farming_plan_task" as task
        INNER JOIN "tabiot_farming_plan_state" as state
          ON task.farming_plan_state = state.name
        INNER JOIN "tabiot_farming_plan" as plan
          ON state.farming_plan = plan.name
        WHERE plan.crop IN (${crops})
          AND task.name = ANY($1)
        `,
        [taskIds]
      );

      const validTaskIds = verifyTasks.map(task => task.task_name);
      const invalidTaskIds = taskIds.filter(id => !validTaskIds.includes(id));

      if (invalidTaskIds.length > 0) {
        console.warn(`Found ${invalidTaskIds.length} invalid/unauthorized task IDs:`, invalidTaskIds);
      }

      const deletedTasks: string[] = [];
      const failedTasks: { taskId: string; error: string }[] = [];
      let totalAllocationsCleanedUp = 0;

      // Process each valid task
      for (const taskId of validTaskIds) {
        try {
          console.log(`Processing task deletion: ${taskId}`);

          // Clean up yield allocations first and count them
          const allocationsCount = await this.cleanupTaskYieldAllocationsWithCount(taskId);
          totalAllocationsCleanedUp += allocationsCount;

          // Delete task and all related resources
          await this.deleteTaskResourcesOnly(taskId);

          deletedTasks.push(taskId);
          console.log(`Successfully deleted task: ${taskId}`);

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error(`Failed to delete task ${taskId}:`, errorMessage);
          failedTasks.push({ taskId, error: errorMessage });
        }
      }

      // Add invalid tasks to failed list
      invalidTaskIds.forEach(taskId => {
        failedTasks.push({
          taskId,
          error: "Task not found or you don't have permission to delete this task"
        });
      });

      const summary = {
        totalRequested: taskIds.length,
        successfullyDeleted: deletedTasks.length,
        failed: failedTasks.length,
        allocationsCleanedUp: totalAllocationsCleanedUp
      };

      console.log(`Bulk deletion completed:`, summary);

      return {
        success: failedTasks.length === 0,
        deletedTasks,
        failedTasks,
        summary
      };

    } catch (error) {
      console.error('Error in bulk task deletion:', error);
      throw new HttpError(500, `Bulk task deletion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Clean up yield allocations when a task is deleted
   * This method ensures that allocated_quantity and remaining_quantity are properly updated
   * when tasks are removed from the system
   */
  private async cleanupTaskYieldAllocations(_user: ICurrentUser, taskId: string): Promise<void> {
    try {
      console.log(`Starting yield allocation cleanup for task: ${taskId}`);

      // Find all allocations linked to this task
      const allocationsToCleanup = await ERPExecute(
        `
        SELECT
          allocation.name as allocation_id,
          allocation.quantity,
          allocation.yield_expected_output_id,
          yieldOutput.allocated_quantity,
          yieldOutput.remaining_quantity
        FROM production_plan_allocation allocation
        INNER JOIN production_plan_yield_expected_output yieldOutput
          ON allocation.yield_expected_output_id = yieldOutput.name
        WHERE allocation.task_id = $1
        `,
        [taskId]
      );

      if (allocationsToCleanup.length === 0) {
        console.log(`No yield allocations found for task: ${taskId}`);
        return;
      }

      console.log(`Found ${allocationsToCleanup.length} allocations to cleanup for task: ${taskId}`);

      // Group allocations by yield output to batch updates
      const yieldOutputUpdates = new Map<string, { totalQuantityToFree: number; allocationsToDelete: string[] }>();

      for (const allocation of allocationsToCleanup) {
        const yieldOutputId = allocation.yield_expected_output_id;
        const quantityToFree = allocation.quantity || 0;

        if (!yieldOutputUpdates.has(yieldOutputId)) {
          yieldOutputUpdates.set(yieldOutputId, {
            totalQuantityToFree: 0,
            allocationsToDelete: []
          });
        }

        const update = yieldOutputUpdates.get(yieldOutputId)!;
        update.totalQuantityToFree += quantityToFree;
        update.allocationsToDelete.push(allocation.allocation_id);
      }

      // Prepare SQL queries for transaction
      const sqlQueries: string[] = [];

      // Delete allocation records
      for (const [yieldOutputId, update] of yieldOutputUpdates) {
        // Delete allocations
        const deleteAllocationSQL = `
          DELETE FROM production_plan_allocation
          WHERE name IN (${update.allocationsToDelete.map(id => `'${id}'`).join(', ')})
        `;
        sqlQueries.push(deleteAllocationSQL);

        // Update yield output quantities
        const updateYieldOutputSQL = `
          UPDATE production_plan_yield_expected_output
          SET
            allocated_quantity = GREATEST(0, allocated_quantity - ${update.totalQuantityToFree}),
            remaining_quantity = remaining_quantity + ${update.totalQuantityToFree}
          WHERE name = '${yieldOutputId}'
        `;
        sqlQueries.push(updateYieldOutputSQL);

        console.log(`Will free ${update.totalQuantityToFree} units from yield output: ${yieldOutputId}`);
      }

      // Execute all cleanup queries in a transaction
      if (sqlQueries.length > 0) {
        await ERPExecuteTransaction(sqlQueries, []);
        console.log(`Successfully cleaned up ${allocationsToCleanup.length} allocations for task: ${taskId}`);
      }

    } catch (error) {
      console.error(`Error cleaning up yield allocations for task ${taskId}:`, error);
      // Don't throw the error to prevent task deletion from failing
      // Log the error but allow the task deletion to proceed
      console.warn(`Yield allocation cleanup failed for task ${taskId}, but task deletion will continue`);
    }
  }

  /**
   * Clean up yield allocations and return the count of cleaned allocations
   * Used by bulk delete to track total allocations cleaned up
   */
  private async cleanupTaskYieldAllocationsWithCount(taskId: string): Promise<number> {
    try {
      console.log(`Starting yield allocation cleanup with count for task: ${taskId}`);

      // Find all allocations linked to this task
      const allocationsToCleanup = await ERPExecute(
        `
        SELECT
          allocation.name as allocation_id,
          allocation.quantity,
          allocation.yield_expected_output_id,
          yieldOutput.allocated_quantity,
          yieldOutput.remaining_quantity
        FROM production_plan_allocation allocation
        INNER JOIN production_plan_yield_expected_output yieldOutput
          ON allocation.yield_expected_output_id = yieldOutput.name
        WHERE allocation.task_id = $1
        `,
        [taskId]
      );

      if (allocationsToCleanup.length === 0) {
        console.log(`No yield allocations found for task: ${taskId}`);
        return 0;
      }

      console.log(`Found ${allocationsToCleanup.length} allocations to cleanup for task: ${taskId}`);

      // Group allocations by yield output to batch updates
      const yieldOutputUpdates = new Map<string, { totalQuantityToFree: number; allocationsToDelete: string[] }>();

      for (const allocation of allocationsToCleanup) {
        const yieldOutputId = allocation.yield_expected_output_id;
        const quantityToFree = allocation.quantity || 0;

        if (!yieldOutputUpdates.has(yieldOutputId)) {
          yieldOutputUpdates.set(yieldOutputId, {
            totalQuantityToFree: 0,
            allocationsToDelete: []
          });
        }

        const update = yieldOutputUpdates.get(yieldOutputId)!;
        update.totalQuantityToFree += quantityToFree;
        update.allocationsToDelete.push(allocation.allocation_id);
      }

      // Prepare SQL queries for transaction
      const sqlQueries: string[] = [];

      // Delete allocation records and update yield outputs
      for (const [yieldOutputId, update] of yieldOutputUpdates) {
        // Delete allocations
        const deleteAllocationSQL = `
          DELETE FROM production_plan_allocation
          WHERE name IN (${update.allocationsToDelete.map(id => `'${id}'`).join(', ')})
        `;
        sqlQueries.push(deleteAllocationSQL);

        // Update yield output quantities
        const updateYieldOutputSQL = `
          UPDATE production_plan_yield_expected_output
          SET
            allocated_quantity = GREATEST(0, allocated_quantity - ${update.totalQuantityToFree}),
            remaining_quantity = remaining_quantity + ${update.totalQuantityToFree}
          WHERE name = '${yieldOutputId}'
        `;
        sqlQueries.push(updateYieldOutputSQL);

        console.log(`Will free ${update.totalQuantityToFree} units from yield output: ${yieldOutputId}`);
      }

      // Execute all cleanup queries in a transaction
      if (sqlQueries.length > 0) {
        await ERPExecuteTransaction(sqlQueries, []);
        console.log(`Successfully cleaned up ${allocationsToCleanup.length} allocations for task: ${taskId}`);
      }

      return allocationsToCleanup.length;

    } catch (error) {
      console.error(`Error cleaning up yield allocations for task ${taskId}:`, error);
      // Don't throw the error to prevent task deletion from failing
      console.warn(`Yield allocation cleanup failed for task ${taskId}, but task deletion will continue`);
      return 0;
    }
  }

  /**
   * Delete task and all related resources without permission checks
   * Used internally by bulk delete after permissions have been verified
   */
  private async deleteTaskResourcesOnly(taskId: string): Promise<void> {
    try {
      // Delete task resources in the correct order to avoid foreign key constraints
      const deleteQueries = [
        // Delete todo items
        `DELETE FROM tabiot_todo WHERE farming_plan_task = '${taskId}'`,

        // Delete assigned users
        `DELETE FROM tabiot_assign_user WHERE task = '${taskId}'`,

        // Delete warehouse items used
        `DELETE FROM tabiot_warehouse_item_task_used WHERE task_id = '${taskId}'`,

        // Delete task worksheets
        `DELETE FROM tabiot_farming_plan_task_worksheet WHERE task_id = '${taskId}'`,

        // Delete production quantities
        `DELETE FROM tabiot_production_quantity WHERE task_id = '${taskId}'`,

        // Delete task item transfers (new structure)
        `DELETE FROM task_item_transfer WHERE source_task_id = '${taskId}' OR target_task_id = '${taskId}'`,

        // Finally delete the task itself
        `DELETE FROM tabiot_farming_plan_task WHERE name = '${taskId}'`
      ];

      await ERPExecuteTransaction(deleteQueries, []);
      console.log(`Successfully deleted all resources for task: ${taskId}`);

    } catch (error) {
      console.error(`Error deleting resources for task ${taskId}:`, error);
      throw error;
    }
  }
}
