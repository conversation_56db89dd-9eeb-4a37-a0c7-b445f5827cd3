import { FrappeService } from '@app/services/frappe/FrappeService';
import Container, { Service } from 'typedi';
import { ICurrentUser } from '@app/interfaces';
import jwt from 'jsonwebtoken';
import configs from '@app/configs';
import axios, { AxiosInstance, CreateAxiosDefaults } from 'axios';
import { HttpError } from 'routing-controllers';
import bodyParser from 'body-parser';
import { UserType } from '@app/utils/enums/usertype';
import { IIotProject } from '@app/interfaces/IIotProject';
import { ERPExecute } from '@app/loaders/pglib/PGDB';
import { IIotZone } from '@app/interfaces/IIotZone';
import { IIotDevice } from '@app/interfaces/IIotDevice';
import { IIotProductionFunction } from '@app/interfaces/IIotProductionFunction';
import { IIotCrop } from '@app/interfaces/IIotCrop';
import { IGeneralDoc } from '@app/interfaces/IGeneralDoc';
import { ICropManagementParams, convertObjectArray } from './CropValidator';
import { verify } from 'crypto';
import { IIotCropNote } from '@app/interfaces/IIotCropNote';
import { OtherService } from '@app/services/frappe/OtherService';
import moment from 'moment';
import e from 'express';
import { SECTION_PERMISSION } from '@app/modules/dynamic-role/enum-role/enum_role';
import { cropParticipantsVerify } from './CropSQLHelper';
import { standardizeDateFormatToYYYY_MM_DD } from '@app/utils/helpers/helper';
import { AppDataSource } from '@app/orm/dataSource';
import { Crop } from '@app/orm/entities/farmingPlan/crop/Crop';
import { IoTTag } from '@app/orm/entities/farmingPlan/tag/Tag';
import { Repository } from 'typeorm';

interface CountParticipant {
  customer_user_id: string;
  first_name: string;
  last_name: string;
  total: number;
}
@Service()
export class CropService {
  private cropRepository: Repository<Crop> = AppDataSource.getRepository(Crop);
  private tagRepository: Repository<IoTTag> = AppDataSource.getRepository(IoTTag);

  constructor(private frappeService: FrappeService, private otherService: OtherService) {
    this.frappeService = Container.get(FrappeService);
    this.otherService = Container.get(OtherService);
  }

  /**
   * Validate tag exists and belongs to user's customer
   */
  private async validateTag(tagName: string, user: ICurrentUser): Promise<boolean> {
    if (!tagName) return true; // Tag is optional

    const tag = await this.tagRepository.findOne({
      where: { name: tagName }
    });

    if (!tag) {
      throw new HttpError(404, `Tag with name ${tagName} not found`);
    }

    if (tag.customer_id !== user.customer_id) {
      throw new HttpError(403, `You don't have permission to use this tag`);
    }

    return true;
  }

  /**
   * Update crop tag in TypeORM database
   */
  private async updateCropTagInTypeORM(cropName: string, tagName: string | undefined): Promise<void> {
    try {
      const crop = await this.cropRepository.findOne({
        where: { name: cropName }
      });

      if (crop) {
        crop.tag = tagName;
        await this.cropRepository.save(crop);
      }
    } catch (error) {
      console.error('Error updating crop tag in TypeORM:', error);
      // Don't throw error here to avoid breaking the main flow
    }
  }

  async getCropByTask(user: ICurrentUser, params: { task_id: string }) {
    try {
      const { task_id } = params;
      const crop = await ERPExecute(
        `
      SELECT
        crop.name,
        crop.label,
        task.name AS task_id,
        task.label AS task_label
      FROM tabiot_crop AS crop
      LEFT JOIN tabiot_farming_plan AS plan ON crop.name = plan.crop
      LEFT JOIN tabiot_farming_plan_state AS state ON plan.name = state.farming_plan
      LEFT JOIN tabiot_farming_plan_task AS task ON state.name = task.farming_plan_state
      WHERE task.name = $1
      `,
        [task_id],
      );
      return crop.length ? crop[0] : null;
    } catch (error: any) {
      throw new HttpError(400, error);
    }
  }

  async getCrop(user: ICurrentUser, params: any) {
    try {
      if (user.user_type === UserType.SYSTEM_USER) {
        // const response = await this.frappeService.callSScript("get-list-location", param, "GET")
        const cropList: any = await this.frappeService.callSScript(
          'general-doc-list',
          {
            filters: JSON.stringify(params.filters) || JSON.stringify([]),
            or_filters: JSON.stringify(params.or_filters) || JSON.stringify([]),
            page: params.page || 1,
            size: params.size || 10000,
            fields: JSON.stringify(params.fields) || JSON.stringify(['*']),
            order_by: params.order_by || null,
            group_by: params.group_by || null,
            doc_name: 'iot_crop',
          },
          'GET',
        );
        return cropList;
      } else {
        /**
         * verify user
         */
        const cropList = await this.otherService.getCropList(user);
        if (cropList.length === 0) {
          return {
            data: [],
            pagination: {
              pageNumber: 1,
              pageSize: 100,
              totalElements: 0,
              totalPages: 0,
            },
          };
        }

        const filters: any = [['iot_crop', 'is_deleted', '=', 0]];

        if (params.filters) {
          const paramsFilterArr: any[] = JSON.parse(params.filters);
          paramsFilterArr.forEach((d: any) => {
            filters.push(d);
          });
        }
        let cropERP: any = await this.frappeService.callSScript(
          'general-doc-list',
          {
            filters: JSON.stringify(filters) || JSON.stringify([]),
            or_filters: JSON.stringify(params.or_filters) || JSON.stringify([]),
            page: params.page || 1,
            size: params.size || 10000,
            fields: JSON.stringify(params.fields) || JSON.stringify(['*']),
            order_by: params.order_by || null,
            group_by: params.group_by || null,
            doc_name: 'iot_crop',
          },
          'GET',
        );

        const { order_by, group_by, page, size } = params;
        const parsePage = page ? parseInt(page) : 1;
        const parseSize = size ? parseInt(size) : 10000;
        let orderBySql = '';
        if (order_by) {
          orderBySql = `ORDER BY ${order_by}`;
        }
        let groupBySql = '';
        if (group_by) {
          groupBySql = `GROUP BY ${group_by}`;
        }

        const cropParticipantList = await cropParticipantsVerify({
          customerId: user.customer_id,
          userId: user.user_id,
          sections: user.sections,
        });
        let cropSQLCondition = '';
        if (cropParticipantList.length > 0) {
          const cropNames = cropParticipantList.map((d: any) => `'${d.name}'`).join(',');
          cropSQLCondition = `AND crop.name IN (${cropNames})`;
        } else {
          return {
            data: [],
            pagination: {
              pageNumber: 1,
              pageSize: 100,
              totalElements: 0,
              totalPages: 0,
            },
          };
        }

        let cropSQL: any = await ERPExecute(
          `
        SELECT
          crop.name,
          crop.plant_id,
          crop.plant_label,
          crop.zone_id,
          crop.zone,
          crop.image,
          crop.label,
          crop.description,
          crop.square,
          crop.start_date,
          crop.end_date,
          crop.quantity_estimate::float,
          crop.status,
          crop.avatar,
          crop.is_deleted,
          crop.document_links,
          crop.location,
          crop.images,
          crop.tag,
          crop.creation,
          crop.modified,
          plan.name AS plan_id,
          plan.label AS plan_label
        FROM
          tabiot_crop AS crop
        LEFT JOIN
          tabiot_farming_plan AS plan ON crop.name = plan.crop
        WHERE TRUE
        ${cropSQLCondition}
        ${orderBySql}
        ${groupBySql}
        LIMIT $1 OFFSET $2
        `,
          [parseSize || 10000, (parsePage - 1) * (parseSize || 10000)],
        );

        const cropSQLCount: any = await ERPExecute(
          `
          SELECT
          crop.name,
          crop.plant_id,
          crop.plant_label,
          crop.zone_id,
          crop.zone,
          crop.image,
          crop.label,
          crop.description,
          crop.square,
          crop.start_date,
          crop.end_date,
          crop.quantity_estimate::float,
          crop.status,
          crop.avatar,
          crop.is_deleted,
          crop.document_links,
          crop.location,
          crop.images,
          crop.tag,
          crop.creation,
          crop.modified,
          plan.name AS plan_id,
          plan.label AS plan_label
        FROM
          tabiot_crop AS crop
        LEFT JOIN
          tabiot_farming_plan AS plan ON crop.name = plan.crop
        WHERE TRUE
        ${cropSQLCondition}
        ${orderBySql}
        ${groupBySql}
        `,
          [],
        );

        const pagination = {
          pageNumber: parsePage || 1,
          pageSize: parseSize || 10000,
          totalElements: cropSQLCount.length,
          totalPages: Math.ceil(cropSQLCount.length / (parseSize || 10000)),
        };

        if (params.filters) {
          console.log('Return erp');
          return cropERP;
        }
        console.log('Return sql');

        return {
          data: cropSQL,
          pagination: pagination,
        };
      }
    } catch (error) {
      throw error;
    }
  }

  async getTemplateCrop(user: ICurrentUser, params: any) {
    try {
      /**
       * verify user
       */
      const cropList = await this.otherService.getCropList(user);
      if (cropList.length === 0) {
        return {
          data: [],
          pagination: {
            pageNumber: 1,
            pageSize: 100,
            totalElements: 0,
            totalPages: 0,
          },
        };
      }

      const filters: any = [['iot_crop', 'is_deleted', '=', 0]];

      if (params.filters) {
        const paramsFilterArr: any[] = JSON.parse(params.filters);
        paramsFilterArr.forEach((d: any) => {
          filters.push(d);
        });
      }
      let cropERP: any = await this.frappeService.callSScript(
        'general-doc-list',
        {
          filters: JSON.stringify(filters) || JSON.stringify([]),
          or_filters: JSON.stringify(params.or_filters) || JSON.stringify([]),
          page: params.page || 1,
          size: params.size || 10000,
          fields: JSON.stringify(params.fields) || JSON.stringify(['*']),
          order_by: params.order_by || null,
          group_by: params.group_by || null,
          doc_name: 'iot_crop',
        },
        'GET',
      );

      const { order_by, group_by, page, size } = params;
      const parsePage = page ? parseInt(page) : 1;
      const parseSize = size ? parseInt(size) : 10000;
      let orderBySql = '';
      if (order_by) {
        orderBySql = `ORDER BY ${order_by}`;
      }
      let groupBySql = '';
      if (group_by) {
        groupBySql = `GROUP BY ${group_by}`;
      }

      const cropParticipantList = await cropParticipantsVerify({
        customerId: user.customer_id,
        userId: user.user_id,
        sections: user.sections,
      });
      let cropSQLCondition = '';
      if (cropParticipantList.length > 0) {
        const cropNames = cropParticipantList.map((d: any) => `'${d.name}'`).join(',');
        cropSQLCondition = `AND crop.name IN (${cropNames})`;
      } else {
        return {
          data: [],
          pagination: {
            pageNumber: 1,
            pageSize: 100,
            totalElements: 0,
            totalPages: 0,
          },
        };
      }

      let cropSQL: any = await ERPExecute(
        `
        SELECT
          crop.name,
          crop.plant_id,
          crop.plant_label,
          crop.zone_id,
          crop.zone,
          crop.image,
          crop.label,
          crop.description,
          crop.square,
          crop.start_date,
          crop.end_date,
          crop.quantity_estimate::float,
          crop.status,
          crop.avatar,
          crop.is_deleted,
          crop.document_links,
          crop.location,
          crop.images,
          crop.tag,
          crop.creation,
          crop.modified,
          plan.name AS plan_id,
          plan.label AS plan_label
        FROM
          tabiot_crop AS crop
        LEFT JOIN
          tabiot_farming_plan AS plan ON crop.name = plan.crop
        WHERE TRUE
        AND crop.is_template = 1
        ${cropSQLCondition}
        ${orderBySql}
        ${groupBySql}
        LIMIT $1 OFFSET $2
        `,
        [parseSize || 10000, (parsePage - 1) * (parseSize || 10000)],
      );

      const cropSQLCount: any = await ERPExecute(
        `
          SELECT
          crop.name,
          crop.plant_id,
          crop.plant_label,
          crop.zone_id,
          crop.zone,
          crop.image,
          crop.label,
          crop.description,
          crop.square,
          crop.start_date,
          crop.end_date,
          crop.quantity_estimate::float,
          crop.status,
          crop.avatar,
          crop.is_deleted,
          crop.document_links,
          crop.location,
          crop.images,
          crop.tag,
          crop.creation,
          crop.modified,
          plan.name AS plan_id,
          plan.label AS plan_label
        FROM
          tabiot_crop AS crop
        LEFT JOIN
          tabiot_farming_plan AS plan ON crop.name = plan.crop
        WHERE TRUE
        AND crop.is_template = 1
        ${cropSQLCondition}
        ${orderBySql}
        ${groupBySql}
        `,
        [],
      );

      const pagination = {
        pageNumber: parsePage || 1,
        pageSize: parseSize || 10000,
        totalElements: cropSQLCount.length,
        totalPages: Math.ceil(cropSQLCount.length / (parseSize || 10000)),
      };

      if (params.filters) {
        console.log('Return erp');
        return cropERP;
      }
      console.log('Return sql');

      return {
        data: cropSQL,
        pagination: pagination,
      };
    } catch (error) {
      throw error;
    }
  }
  // async getStatisticParticipants1(user: ICurrentUser, params: any) {
  //   try {
  //     const cropId = params.crop_id;
  //     let cropSql = cropId ? `AND crop.name = '${cropId}'` : '';

  //     const getCountQuery = (joinTable: string, additionalCondition: string = '') => `
  //       SELECT
  //         cus_user.name AS customer_user_id,
  //         cus_user.first_name,
  //         cus_user.last_name,
  //         COUNT(cus_user.name) AS total
  //       FROM
  //         tabiot_customer_user AS cus_user
  //       INNER JOIN
  //         tabiot_customer AS cus ON cus_user.customer_id = cus.name
  //       INNER JOIN
  //         tabiot_zone AS zone ON cus.name = zone.customer_id
  //       INNER JOIN
  //         tabiot_crop AS crop ON zone.name = crop.zone_id
  //       LEFT JOIN
  //         tabiot_farming_plan AS plan ON crop.name = plan.crop
  //       LEFT JOIN
  //         tabiot_farming_plan_state AS plan_state ON plan.name = plan_state.farming_plan
  //       LEFT JOIN
  //         tabiot_farming_plan_task AS task ON plan_state.name = task.farming_plan_state
  //       ${joinTable}
  //       WHERE TRUE
  //         ${cropSql}
  //         AND crop.is_deleted = 0
  //         ${additionalCondition}
  //       GROUP BY
  //         cus_user.name
  //     `;

  //     const countAssignedInTask = await ERPExecute(
  //       getCountQuery(
  //         'LEFT JOIN tabiot_assign_user AS assign_user ON task.name = assign_user.task',
  //         'AND assign_user.customer_user = cus_user.name',
  //       ),
  //       [],
  //     );

  //     const countInvoledInTask = await ERPExecute(
  //       getCountQuery(
  //         'LEFT JOIN tabiot_assign_user AS assign_user ON task.name = assign_user.task AND assign_user.customer_user = cus_user.name',
  //       ),
  //       [],
  //     );

  //     const countAssignedInTaskChild = await ERPExecute(
  //       getCountQuery(
  //         'LEFT JOIN tabiot_todo AS todo ON task.name = todo.farming_plan_task AND todo.customer_user_id = cus_user.name',
  //       ),
  //       [],
  //     );

  //     const mergeCounts = (countArray: any[], propertyName: string) => {
  //       const countObj: any = {};
  //       countArray.forEach((item: any) => {
  //         const key = item.customer_user_id;
  //         countObj[key] = countObj[key] || {
  //           customer_user_id: key,
  //           first_name: item.first_name,
  //           last_name: item.last_name,
  //         };
  //         countObj[key][propertyName] = item.total;
  //       });
  //       return Object.values(countObj);
  //     };

  //     const countAssignedInTaskArr = mergeCounts(countAssignedInTask, 'total_assign_task');
  //     const countInvoledInTaskArr = mergeCounts(countInvoledInTask, 'total_involed_task');
  //     const countAssignedInTaskChildArr = mergeCounts(countAssignedInTaskChild, 'total_assign_task_child');

  //     const countArr = [...countAssignedInTaskArr, ...countInvoledInTaskArr, ...countAssignedInTaskChildArr];

  //     const startIndex = (params.page - 1) * params.size || 0;
  //     const endIndex = startIndex + (params.size || 10000);
  //     const paginatedCount = countArr.slice(startIndex, endIndex);

  //     const totalElements = countArr.length;
  //     const totalPages = Math.ceil(totalElements / (params.size || 10000));
  //     const pagination = {
  //       pageNumber: params.page || 1,
  //       pageSize: params.size || 10000,
  //       totalElements: totalElements,
  //       totalPages: totalPages,
  //     };

  //     return {
  //       data: paginatedCount.map((item: any) => ({
  //         customer_user_id: item.customer_user_id,
  //         first_name: item.first_name,
  //         last_name: item.last_name,
  //         total_assign_task: item.total_assign_task || '0',
  //         total_involed_task: item.total_involed_task || '0',
  //         total_assign_task_child: item.total_assign_task_child || '0',
  //       })),
  //       pagination: pagination,
  //     };
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  async getStatisticParticipants(user: ICurrentUser, params: any) {
    try {
      const cropId = params.crop_id;
      let cropSql = '';
      if (cropId) {
        cropSql = `AND crop.name = '${cropId}'`;
      }
      const page = params.page || 1;
      const size = params.size || 10000;
      const countAssignedInTask = await ERPExecute(
        `
        SELECT
        cus_user.name AS customer_user_id,
        cus_user.first_name AS first_name,
        cus_user.last_name AS last_name,
        cus_user.email AS email,
        COUNT(cus_user.name) AS total
      FROM
        tabiot_customer_user AS cus_user
      INNER JOIN
        tabiot_customer AS cus ON cus_user.customer_id = cus.name
      INNER JOIN
        tabiot_zone AS zone ON cus.name = zone.customer_id
      INNER JOIN
        tabiot_crop AS crop ON zone.name = crop.zone_id
      LEFT JOIN
        tabiot_farming_plan AS plan ON crop.name = plan.crop
      LEFT JOIN
        tabiot_farming_plan_state AS plan_state ON plan.name = plan_state.farming_plan
      LEFT JOIN
        tabiot_farming_plan_task AS task ON plan_state.name = task.farming_plan_state
      WHERE TRUE
          ${cropSql}
          AND crop.is_deleted = 0
          AND task.assigned_to = cus_user.name
      GROUP BY
        cus_user.name
  `,
        [],
      );

      const countInvoledInTask = await ERPExecute(
        `
        SELECT
        cus_user.name AS customer_user_id,
        cus_user.first_name AS first_name,
        cus_user.last_name AS last_name,
        cus_user.email AS email,
        COUNT(cus_user.name) AS total
      FROM
        tabiot_customer_user AS cus_user
      INNER JOIN
        tabiot_customer AS cus ON cus_user.customer_id = cus.name
      INNER JOIN
        tabiot_zone AS zone ON cus.name = zone.customer_id
      INNER JOIN
        tabiot_crop AS crop ON zone.name = crop.zone_id
      LEFT JOIN
        tabiot_farming_plan AS plan ON crop.name = plan.crop
      LEFT JOIN
        tabiot_farming_plan_state AS plan_state ON plan.name = plan_state.farming_plan
      LEFT JOIN
        tabiot_farming_plan_task AS task ON plan_state.name = task.farming_plan_state
      LEFT JOIN 
        tabiot_assign_user AS assign_user 
          ON task.name = assign_user.task AND assign_user.customer_user = cus_user.name
      WHERE TRUE
          ${cropSql}
          AND crop.is_deleted = 0
          AND assign_user.customer_user = cus_user.name
      GROUP BY
        cus_user.name
  `,
        [],
      );

      const countAssignedInTaskChild = await ERPExecute(
        `
        SELECT
        cus_user.name AS customer_user_id,
        cus_user.first_name AS first_name,
        cus_user.last_name AS last_name,
        cus_user.email AS email,
        COUNT(cus_user.name) AS total
      FROM
        tabiot_customer_user AS cus_user
      INNER JOIN
        tabiot_customer AS cus ON cus_user.customer_id = cus.name
      INNER JOIN
        tabiot_zone AS zone ON cus.name = zone.customer_id
      INNER JOIN
        tabiot_crop AS crop ON zone.name = crop.zone_id
      LEFT JOIN
        tabiot_farming_plan AS plan ON crop.name = plan.crop
      LEFT JOIN
        tabiot_farming_plan_state AS plan_state ON plan.name = plan_state.farming_plan
      LEFT JOIN
        tabiot_farming_plan_task AS task ON plan_state.name = task.farming_plan_state
      LEFT JOIN
        tabiot_todo AS todo ON task.name = todo.farming_plan_task AND todo.customer_user_id = cus_user.name
      WHERE TRUE
          ${cropSql}
          AND crop.is_deleted = 0
          AND todo.customer_user_id = cus_user.name
      GROUP BY
        cus_user.name
  `,
        [],
      );
      // Create a set of all unique customer_user_ids
      const allCustomerUserIds = new Set([
        ...countAssignedInTask.map(item => item.customer_user_id),
        ...countInvoledInTask.map(item => item.customer_user_id),
        ...countAssignedInTaskChild.map(item => item.customer_user_id),
      ]);

      // Convert the results to maps for easy lookup
      const assignedInTaskMap = new Map(countAssignedInTask.map(item => [item.customer_user_id, item]));
      const involedInTaskMap = new Map(countInvoledInTask.map(item => [item.customer_user_id, item]));
      const assignedInTaskChildMap = new Map(countAssignedInTaskChild.map(item => [item.customer_user_id, item]));

      // Merge the results
      const countArr = Array.from(allCustomerUserIds).map(customer_user_id => {
        const assignedInTaskItem = assignedInTaskMap.get(customer_user_id) || {};
        const involedInTaskItem = involedInTaskMap.get(customer_user_id) || {};
        const assignedInTaskChildItem = assignedInTaskChildMap.get(customer_user_id) || {};
        return {
          customer_user_id: customer_user_id,
          first_name:
            assignedInTaskItem.first_name || involedInTaskItem.first_name || assignedInTaskChildItem.first_name || '',
          last_name:
            assignedInTaskItem.last_name || involedInTaskItem.last_name || assignedInTaskChildItem.last_name || '',
          full_name: `${assignedInTaskItem.first_name || involedInTaskItem.first_name || assignedInTaskChildItem.first_name || ''
            } ${assignedInTaskItem.last_name || involedInTaskItem.last_name || assignedInTaskChildItem.last_name || ''}`,
          total_assign_task: assignedInTaskItem.total || '0',
          total_involed_task: involedInTaskItem.total || '0',
          total_assign_task_child: assignedInTaskChildItem.total || '0',
          email: assignedInTaskItem.email || involedInTaskItem.email || assignedInTaskChildItem.email || '',
        };
      });

      // Handle pagination
      const startIndex = (page - 1) * size;
      const endIndex = startIndex + size;
      const paginatedCount = countArr.slice(startIndex, endIndex);
      const totalPages = Math.ceil(countArr.length / size);
      return {
        data: paginatedCount,
        pagination: {
          totalElements: countArr.length,
          pageNumber: page,
          pageSize: size,
          totalPages,
        },
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * 
   * @param user 
   * @param params 
   * @returns 
   * {
        "task_id": "07a24765-b1a7-4472-8e5d-853e8c0600a8",
        "task_label": "Kiểm tra & theo dõi",
        "start_date": "2024-03-11T00:30:48.000Z",
        "end_date": "2024-03-11T09:30:57.000Z",
        "description": "",
        "customer_user_id": "06a5df71-945e-44c7-a5ae-0da53aec7c44",
        "todos": [
            {
                "todo_id": "1d435c34-f5e2-4099-b5d5-870d508d9abe",
                "todo_label": "Phân loại nhiễm",
                "todo_description": null
            }
        ]
    },
   */
  async getParticipantsTaskList(user: ICurrentUser, params: any) {
    try {
      const cropId = params.crop_id;
      let cropSql = '';
      if (cropId) {
        cropSql = `AND crop.name = '${cropId}'`;
      }

      const customerUserId = params.customer_user_id;
      let customerUserSql = '';
      if (customerUserId) {
        customerUserSql = `AND cus_user.name = '${customerUserId}'`;
      }
      let customerUserSql2 = '';
      if (customerUserId) {
        customerUserSql2 = `AND assign_user.customer_user = '${customerUserId}'`;
      }
      const page = params.page || 1;
      const size = params.size || 10000;
      const countAssignedInTask = await ERPExecute(
        `
        SELECT
        task.name AS task_id,
        task.label AS task_label,
        task.start_date,
        task.end_date,
        task.description,
        cus_user.name AS customer_user_id
      FROM
        tabiot_customer_user AS cus_user
      INNER JOIN
        tabiot_customer AS cus ON cus_user.customer_id = cus.name
      INNER JOIN
        tabiot_zone AS zone ON cus.name = zone.customer_id
      INNER JOIN
        tabiot_crop AS crop ON zone.name = crop.zone_id
      LEFT JOIN
        tabiot_farming_plan AS plan ON crop.name = plan.crop
      LEFT JOIN
        tabiot_farming_plan_state AS plan_state ON plan.name = plan_state.farming_plan
      LEFT JOIN
        tabiot_farming_plan_task AS task ON plan_state.name = task.farming_plan_state
      WHERE TRUE
          ${cropSql}
          AND crop.is_deleted = 0
          AND task.assigned_to = cus_user.name
          ${customerUserSql}
      GROUP BY
        task.name, cus_user.name
      `,
        [],
      );

      const countInvoledInTask = await ERPExecute(
        `
      SELECT
        task.name AS task_id,
        task.label AS task_label,
        task.start_date,
        task.end_date,
        task.description,
        cus_user.name AS customer_user_id
      FROM
        tabiot_customer_user AS cus_user
      INNER JOIN
        tabiot_customer AS cus ON cus_user.customer_id = cus.name
      INNER JOIN
        tabiot_zone AS zone ON cus.name = zone.customer_id
      INNER JOIN
        tabiot_crop AS crop ON zone.name = crop.zone_id
      LEFT JOIN
        tabiot_farming_plan AS plan ON crop.name = plan.crop
      LEFT JOIN
        tabiot_farming_plan_state AS plan_state ON plan.name = plan_state.farming_plan
      LEFT JOIN
        tabiot_farming_plan_task AS task ON plan_state.name = task.farming_plan_state
      LEFT JOIN 
        tabiot_assign_user AS assign_user 
          ON task.name = assign_user.task AND assign_user.customer_user = cus_user.name
      WHERE TRUE
          ${cropSql}
          AND crop.is_deleted = 0
          AND assign_user.customer_user = cus_user.name
          ${customerUserSql2}
      GROUP BY
        task.name, cus_user.name
      `,
        [],
      );

      const countAssignedInTaskChild = await ERPExecute(
        `
        SELECT
          task.name AS task_id,
          task.label AS task_label,
          task.start_date,
          task.end_date,
          task.description,
          todo.customer_user_id,
          json_agg(json_build_object(
            'todo_id', todo.name,
            'todo_label', todo.label,
            'todo_description', todo.description
          )) FILTER (WHERE todo.name IS NOT NULL) AS todos
        FROM
          tabiot_customer_user AS cus_user
        INNER JOIN
          tabiot_customer AS cus ON cus_user.customer_id = cus.name
        INNER JOIN
          tabiot_zone AS zone ON cus.name = zone.customer_id
        INNER JOIN
          tabiot_crop AS crop ON zone.name = crop.zone_id
        LEFT JOIN
          tabiot_farming_plan AS plan ON crop.name = plan.crop
        LEFT JOIN
          tabiot_farming_plan_state AS plan_state ON plan.name = plan_state.farming_plan
        LEFT JOIN
          tabiot_farming_plan_task AS task ON plan_state.name = task.farming_plan_state
        LEFT JOIN
          tabiot_todo AS todo ON task.name = todo.farming_plan_task AND todo.customer_user_id = cus_user.name
        WHERE TRUE
            ${cropSql}
            AND crop.is_deleted = 0
            AND todo.customer_user_id = cus_user.name
            ${customerUserSql}
            
        GROUP BY
          task.name, todo.customer_user_id
      `,
        [],
      );
      // return { countAssignedInTask, countInvoledInTask, countAssignedInTaskChild };
      // Create a set of all unique task_ids
      const allCustomerUserIds = new Set([
        ...countAssignedInTask.map(item => item.task_id),
        ...countInvoledInTask.map(item => item.task_id),
        ...countAssignedInTaskChild.map(item => item.task_id),
      ]);

      // Convert the results to maps for easy lookup
      const assignedInTaskMap = new Map(countAssignedInTask.map(item => [item.task_id, item]));
      const involedInTaskMap = new Map(countInvoledInTask.map(item => [item.task_id, item]));
      const assignedInTaskChildMap = new Map(countAssignedInTaskChild.map(item => [item.task_id, item]));
      // Merge the results
      const countArr = Array.from(allCustomerUserIds).map(task_id => {
        const assignedInTaskItem = assignedInTaskMap.get(task_id) || {};
        const involedInTaskItem = involedInTaskMap.get(task_id) || {};
        const assignedInTaskChildItem = assignedInTaskChildMap.get(task_id) || {};

        return {
          task_id: assignedInTaskItem.task_id || involedInTaskItem.task_id || assignedInTaskChildItem.task_id || '',
          task_label:
            assignedInTaskItem.task_label || involedInTaskItem.task_label || assignedInTaskChildItem.task_label || '',
          start_date: assignedInTaskItem.start_date || involedInTaskItem.start_date || '',
          end_date: assignedInTaskItem.end_date || involedInTaskItem.end_date || '',
          description: assignedInTaskItem.description || involedInTaskItem.description || '',
          customer_user_id: assignedInTaskItem.customer_user_id || involedInTaskItem.customer_user_id || '',
          todos: assignedInTaskChildItem.todos || [],
        };
      });

      // Handle pagination
      const startIndex = (page - 1) * size;
      const endIndex = startIndex + size;
      const paginatedCount = countArr.slice(startIndex, endIndex);
      const totalPages = Math.ceil(countArr.length / size);
      return {
        data: paginatedCount,
        pagination: {
          totalElements: countArr.length,
          pageNumber: page,
          pageSize: size,
          totalPages,
        },
      };
    } catch (error) {
      throw error;
    }
  }

  async getStatisticPestList(user: ICurrentUser, params: any) {
    try {
      const cropId = params.crop_id;
      const page = params.page || 1;
      const size = params.size || 10000;
      const res = await ERPExecute(
        `
      SELECT
        pest.name AS pest_id,
        pest.label AS pest_label,
        pest.image AS image,
        category.name AS category_id,
        category.label AS category_label,
        state.name AS state_id,
        state.label AS state_label,
        cus_user.name AS customer_user_id,
        cus_user.first_name AS first_name,
        cus_user.last_name AS last_name
      FROM
        tabiot_pest AS pest
      INNER JOIN
        tabiot_crop AS crop
      ON pest.iot_crop = crop.name
      LEFT JOIN
        tabiot_category_in_pest AS cate_in_pest
      ON pest.name = cate_in_pest.iot_pest
      LEFT JOIN
        tabiot_category AS category
      ON cate_in_pest.iot_category = category.name
      LEFT JOIN
        tabiot_state_in_pest AS state_in_pest
      ON pest.name = state_in_pest.iot_pest
      LEFT JOIN
        tabiot_farming_plan_state AS state
      ON state_in_pest.iot_farming_plan_state = state.name
      LEFT JOIN
        tabiot_user_in_pest AS user_in_pest
      ON pest.name = user_in_pest.iot_pest
      LEFT JOIN
        tabiot_customer_user AS cus_user
      ON user_in_pest.iot_customer_user = cus_user.name
      WHERE
        crop.name = $1
      GROUP BY
        crop.name,
        pest.name,
        category.name,
        state.name,
        cus_user.name
      ORDER BY
        pest.label
      LIMIT $2  OFFSET $3
        ;
       `,
        [cropId, size, (page - 1) * size],
      );
      const count = await ERPExecute(
        `
      SELECT
        pest.name AS pest_id,
        pest.label AS pest_label,
        category.name AS category_id,
        category.label AS category_label,
        state.name AS state_id,
        state.label AS state_label,
        cus_user.name AS customer_user_id,
        cus_user.first_name AS first_name,
        cus_user.last_name AS last_name
      FROM
        tabiot_pest AS pest
      INNER JOIN
        tabiot_crop AS crop
      ON pest.iot_crop = crop.name
      LEFT JOIN
        tabiot_category_in_pest AS cate_in_pest
      ON pest.name = cate_in_pest.iot_pest
      LEFT JOIN
        tabiot_category AS category
      ON cate_in_pest.iot_category = category.name
      LEFT JOIN
        tabiot_state_in_pest AS state_in_pest
      ON pest.name = state_in_pest.iot_pest
      LEFT JOIN
        tabiot_farming_plan_state AS state
      ON state_in_pest.iot_farming_plan_state = state.name
      LEFT JOIN
        tabiot_user_in_pest AS user_in_pest
      ON pest.name = user_in_pest.iot_pest
      LEFT JOIN
        tabiot_customer_user AS cus_user
      ON user_in_pest.iot_customer_user = cus_user.name
      WHERE
        crop.name = $1
      GROUP BY
        crop.name,
        pest.name,
        category.name,
        state.name,
        cus_user.name
      ORDER BY
        pest.label;
       `,
        [cropId],
      );
      const totalElements = count.length;
      const totalPages = Math.ceil(totalElements / size);
      const pagination = {
        pageNumber: page,
        pageSize: size,
        totalElements: totalElements,
        totalPages: totalPages,
      };
      return {
        data: res,
        pagination: pagination,
      };
    } catch (error) {
      throw error;
    }
  }

  async getStatisticNoteList(user: ICurrentUser, params: any) {
    try {
      const cropId = params.crop_id;
      const page = params.page || 1;
      const size = params.size || 10000;
      const res = await ERPExecute(
        `
      SELECT
        note.name AS note_id,
        note.label AS note_label,
        note.image AS image,
        note.note,
        note.creation,
        note.modified
      FROM
        "tabiot_Crop_note" AS note
      INNER JOIN
        tabiot_crop AS crop
      ON note.crop = crop.name
      WHERE
        crop.name = $1
      GROUP BY
        note.name,
        crop.name
      ORDER BY
        note.label
      LIMIT $2  OFFSET $3
        ;
       `,
        [cropId, size, (page - 1) * size],
      );
      const count = await ERPExecute(
        `
      SELECT
        note.name AS note_id,
        note.label AS note_label,
        note.note
      FROM
        "tabiot_Crop_note" AS note
      INNER JOIN
        tabiot_crop AS crop
      ON note.crop = crop.name
      WHERE
        crop.name = $1
      GROUP BY
        note.name,
        crop.name
      ORDER BY
        note.label
        `,
        [cropId],
      );
      const totalElements = count.length;
      const totalPages = Math.ceil(totalElements / size);
      const pagination = {
        pageNumber: page,
        pageSize: size,
        totalElements: totalElements,
        totalPages: totalPages,
      };
      return {
        data: res,
        pagination: pagination,
      };
    } catch (error) {
      throw error;
    }
  }

  async getStatisticUserInvoledInTask(user: ICurrentUser, params: any) {
    try {
      const cropId = params.crop_id;
      const page = params.page || 1;
      const size = params.size || 10000;
      const res = await ERPExecute(
        `
      SELECT
        cus_user.first_name,
        cus_user.last_name
      FROM
        tabiot_customer_user AS cus_user
      INNER JOIN
        tabiot_assign_user AS involed_user
      ON cus_user.name = involed_user.customer_user
      INNER JOIN
        tabiot_farming_plan_task AS task
      ON involed_user.task = task.name
      INNER JOIN
        tabiot_farming_plan_state AS plan_state
      ON task.farming_plan_state = plan_state.name
      INNER JOIN
        tabiot_farming_plan AS plan
      ON plan_state.farming_plan = plan.name
      INNER JOIN
        tabiot_crop AS crop
      ON crop.name = plan.crop
      WHERE
        crop.name = $1
      GROUP BY
        cus_user.name
        ;
       `,
        [cropId],
      );
      const count = await ERPExecute(
        `
      SELECT
        note.name AS note_id,
        note.label AS note_label
      FROM
        "tabiot_Crop_note" AS note
      INNER JOIN
        tabiot_crop AS crop
      ON note.crop = crop.name
      WHERE
        crop.name = $1
      GROUP BY
        note.name,
        crop.name
      ORDER BY
        note.label
        `,
        [cropId],
      );
      const totalElements = count.length;
      const totalPages = Math.ceil(totalElements / size);
      const pagination = {
        pageNumber: page,
        pageSize: size,
        totalElements: totalElements,
        totalPages: totalPages,
      };
      return {
        data: res,
      };
    } catch (error) {
      throw error;
    }
  }

  async getStatisticProductQuantity(user: ICurrentUser, params: any) {
    try {
      const cropId = params.crop_id;
      const res = await ERPExecute(
        `
        WITH task_temp AS (
          SELECT task.name AS task_id
          FROM "tabiot_farming_plan_task" AS task
          INNER JOIN tabiot_farming_plan_state AS plan_state
              ON task.farming_plan_state = plan_state.name
          INNER JOIN tabiot_farming_plan AS plan
              ON plan_state.farming_plan = plan.name
          INNER JOIN tabiot_crop AS crop
              ON crop.name = plan.crop
          WHERE crop.name = $1
      )
      
      SELECT
          agri_product.name AS agri_product_name,
          agri_product.label AS agri_product_label,
          agri_product_group.name AS agri_product_group_name,
          agri_product_group.label AS agri_product_group_label,
          SUM(prod_quan.exp_quantity) AS total_exp_quantity,
          SUM(prod_quan.draft_quantity) AS total_draft_quantity,
          MAX(prod_quan.total_qty_in_crop) + SUM(prod_quan.issued_quantity) AS total_quantity,
          MAX(prod_quan.total_qty_in_crop) AS total_real_quantity,
          SUM(prod_quan.issued_quantity) AS total_issued_quantity,
          SUM(prod_quan.finished_quantity) AS total_finished_quantity,
          agri_product.valuation_rate AS valuation_rate,
          unit.uom_name AS unit_label,
          unit.name AS unit_id
      FROM
          tabiot_production_quantity AS prod_quan
      INNER JOIN task_temp
          ON prod_quan.task_id = task_temp.task_id
      INNER JOIN "tabItem" AS agri_product
          ON prod_quan.product_id = agri_product.name
      INNER JOIN "tabItem Group" AS agri_product_group
          ON agri_product.item_group = agri_product_group.name
      INNER JOIN "tabUOM" AS unit
          ON agri_product.stock_uom = unit.name
      GROUP BY
          agri_product.name,
          agri_product_group.name,
          unit.name,
          unit.uom_name
      ORDER BY
          agri_product_group.label,
          agri_product.label;
      ;
      
      `,
        [cropId],
      );
      const formattedResult = res.map((item: any) => ({
        ...item,
        total_loss_quantity: parseFloat(item.total_loss_quantity),
        total_exp_quantity: parseFloat(item.total_exp_quantity),
        total_quantity: parseFloat(item.total_quantity),
      }));
      return formattedResult;
    } catch (error) {
      throw error;
    }
  }

  async getCropProductDetailStatistic(user: ICurrentUser, params: any) {
    try {
      const cropId = params.crop_id;
      const productId = params.product_id;
      const page = params.page || 1;
      const size = params.size || 10000;
      const res = await ERPExecute(
        `
        WITH task_temp AS (
          SELECT 
            task.name AS task_id,
            task.label AS task_label
          FROM "tabiot_farming_plan_task" AS task
          INNER JOIN tabiot_farming_plan_state AS plan_state
              ON task.farming_plan_state = plan_state.name
          INNER JOIN tabiot_farming_plan AS plan
              ON plan_state.farming_plan = plan.name
          INNER JOIN tabiot_crop AS crop
              ON crop.name = plan.crop
          WHERE crop.name = $1
      )

      SELECT
          task_temp.task_id,
          task_temp.task_label,
          agri_product.label AS agri_product_label,
          prod_quan.exp_quantity AS total_exp_quantity,
          prod_quan.quantity + prod_quan.issued_quantity AS total_quantity,
          prod_quan.quantity AS total_real_quantity,
          prod_quan.issued_quantity AS total_issued_quantity,
          prod_quan.finished_quantity AS total_finished_quantity,
          prod_quan.draft_quantity AS total_draft_quantity,
          agri_product.valuation_rate AS valuation_rate,
          unit.uom_name AS unit_label,
          unit.name AS unit_id
      FROM
          tabiot_production_quantity AS prod_quan
      INNER JOIN task_temp
          ON prod_quan.task_id = task_temp.task_id
      INNER JOIN "tabItem" AS agri_product
          ON prod_quan.product_id = agri_product.name
      INNER JOIN "tabUOM" AS unit
          ON agri_product.stock_uom = unit.name
      WHERE
          agri_product.name = $2
      LIMIT $3  OFFSET $4;
      `,
        [cropId, productId, size, (page - 1) * size],
      );
      const count = await ERPExecute(
        `
        WITH task_temp AS (
          SELECT task.name AS task_id
          FROM "tabiot_farming_plan_task" AS task
          INNER JOIN tabiot_farming_plan_state AS plan_state
              ON task.farming_plan_state = plan_state.name
          INNER JOIN tabiot_farming_plan AS plan
              ON plan_state.farming_plan = plan.name
          INNER JOIN tabiot_crop AS crop
              ON crop.name = plan.crop
          WHERE crop.name = $1
      )

      SELECT
          agri_product.label AS agri_product_label,
          prod_quan.exp_quantity AS total_exp_quantity,
          prod_quan.quantity + prod_quan.issued_quantity AS total_quantity,
          prod_quan.quantity AS total_real_quantity,
          prod_quan.issued_quantity AS total_issued_quantity,
          prod_quan.finished_quantity AS total_finished_quantity,
          agri_product.valuation_rate AS valuation_rate,
          unit.uom_name AS unit_label,
          unit.name AS unit_id
      FROM
          tabiot_production_quantity AS prod_quan
      INNER JOIN task_temp
          ON prod_quan.task_id = task_temp.task_id
      INNER JOIN "tabItem" AS agri_product
          ON prod_quan.product_id = agri_product.name
      INNER JOIN "tabUOM" AS unit
          ON agri_product.stock_uom = unit.name
      WHERE
          agri_product.name = $2
      `,
        [cropId, productId],
      );
      const formattedResult = res.map((item: any) => ({
        ...item,
        total_exp_quantity: parseFloat(item.total_exp_quantity),
        total_quantity: parseFloat(item.total_quantity),
        total_loss_quantity: parseFloat(item.total_loss_quantity),
      }));
      const totalElements = count.length;
      const totalPages = Math.ceil(totalElements / size);
      const pagination = {
        pageNumber: page,
        pageSize: size,
        totalElements: totalElements,
        totalPages: totalPages,
      };
      return {
        data: formattedResult,
        pagination: pagination,
      };
    } catch (error) {
      throw error;
    }
  }

  async getCropItemDetailStatistic(user: ICurrentUser, params: any) {
    try {
      const cropId = params.crop_id;
      const categoryId = params.category_id;
      const page = params.page || 1;
      const size = params.size || 10000;
      const res = await ERPExecute(
        `
        WITH task_temp AS (
          SELECT
              task.name AS task_id,
              task.label AS task_label,
              task.start_date,
              task.end_date,
              plan_state.name AS state_id,
              plan_state.label AS state_label,
              plan.name AS plan_id,
              plan.label AS plan_label
          FROM "tabiot_farming_plan_task" AS task
          INNER JOIN tabiot_farming_plan_state AS plan_state
              ON task.farming_plan_state = plan_state.name
          INNER JOIN tabiot_farming_plan AS plan
              ON plan_state.farming_plan = plan.name
          INNER JOIN tabiot_crop AS crop
              ON crop.name = plan.crop
          WHERE crop.name = $1
      )

      SELECT
          task_temp.task_id,
          task_temp.task_label,
          task_temp.start_date,
          task_temp.end_date,
          task_temp.state_label,
          task_temp.plan_label,
          category.name AS category_id,
          category.label AS category_label,
          cate_group.name AS category_group_id,
          cate_group.label AS category_group_label,
          item.exp_quantity AS total_exp_quantity,
          item.quantity + item.issued_quantity AS total_quantity,
          item.quantity AS total_real_quantity,
          item.issued_quantity AS total_issued_quantity,
          item.draft_quantity AS total_draft_quantity,
          unit.uom_name AS unit_label,
          unit.name AS unit_id
      FROM
          tabiot_warehouse_item_task_used AS item
      INNER JOIN task_temp
          ON item.task_id = task_temp.task_id
      INNER JOIN "tabItem" AS category
          ON item.iot_category_id = category.name
      INNER JOIN "tabItem Group" AS cate_group
          ON category.item_group = cate_group.name
      INNER JOIN "tabUOM" AS unit
          ON category.stock_uom = unit.name
      WHERE
          category.name = $2
      LIMIT $3  OFFSET $4;
      `,
        [cropId, categoryId, size, (page - 1) * size],
      );
      const count = await ERPExecute(
        `
        WITH task_temp AS (
          SELECT
              task.name AS task_id,
              task.label AS task_label,
              task.start_date,
              task.end_date,
              plan_state.name AS state_id,
              plan_state.label AS state_label,
              plan.name AS plan_id,
              plan.label AS plan_label
          FROM "tabiot_farming_plan_task" AS task
          INNER JOIN tabiot_farming_plan_state AS plan_state
              ON task.farming_plan_state = plan_state.name
          INNER JOIN tabiot_farming_plan AS plan
              ON plan_state.farming_plan = plan.name
          INNER JOIN tabiot_crop AS crop
              ON crop.name = plan.crop
          WHERE crop.name = $1
      )

      SELECT
          task_temp.task_id,
          task_temp.task_label,
          task_temp.start_date,
          task_temp.end_date,
          task_temp.state_label,
          task_temp.plan_label,
          category.name AS category_id,
          category.label AS category_label,
          cate_group.name AS category_group_id,
          cate_group.label AS category_group_label,
          item.exp_quantity AS total_exp_quantity,
          item.quantity + item.loss_quantity AS total_quantity,
          item.loss_quantity AS total_loss_quantity,
          unit.uom_name AS unit_label,
          unit.name AS unit_id
      FROM
          tabiot_warehouse_item_task_used AS item
      INNER JOIN task_temp
          ON item.task_id = task_temp.task_id
      INNER JOIN "tabItem" AS category
          ON item.iot_category_id = category.name
      INNER JOIN "tabItem Group" AS cate_group
          ON category.item_group = cate_group.name
      INNER JOIN "tabUOM" AS unit
          ON category.stock_uom = unit.name
      WHERE
          category.name = $2;
      `,
        [cropId, categoryId],
      );
      const formattedResult = res.map((item: any) => ({
        ...item,
        total_exp_quantity: parseFloat(item.total_exp_quantity),
        total_quantity: parseFloat(item.total_quantity),
        total_loss_quantity: parseFloat(item.total_loss_quantity),
      }));
      const totalElements = count.length;
      const totalPages = Math.ceil(totalElements / size);
      const pagination = {
        pageNumber: page,
        pageSize: size,
        totalElements: totalElements,
        totalPages: totalPages,
      };
      return {
        data: formattedResult,
        pagination: pagination,
      };
    } catch (error) {
      throw error;
    }
  }

  async getStatisticItem(user: ICurrentUser, params: any) {
    try {
      const cropId = params.crop_id;
      const res = await ERPExecute(
        `
        WITH task_temp AS (
          SELECT task.name AS task_id
          FROM "tabiot_farming_plan_task" AS task
          INNER JOIN tabiot_farming_plan_state AS plan_state
              ON task.farming_plan_state = plan_state.name
          INNER JOIN tabiot_farming_plan AS plan
              ON plan_state.farming_plan = plan.name
          INNER JOIN tabiot_crop AS crop
              ON crop.name = plan.crop
          WHERE crop.name = $1
      )
      SELECT
          category.name AS category_name,
          category.label AS category_label,
          category.image AS category_image,
          category.valuation_rate AS valuation_rate,
          cate_group.name AS category_group_name,
          cate_group.label AS category_group_label,
          SUM(item.exp_quantity) AS total_exp_quantity,
          SUM(item.draft_quantity) AS total_draft_quantity,
          MAX(item.total_qty_in_crop) + SUM(item.issued_quantity) AS total_quantity,
          MAX(item.total_qty_in_crop) AS total_real_quantity,
          SUM(item.issued_quantity) AS total_issued_quantity,
          unit.uom_name AS unit_label,
          unit.name AS unit_id
      FROM
          tabiot_warehouse_item_task_used AS item
      INNER JOIN task_temp
          ON item.task_id = task_temp.task_id
      INNER JOIN "tabItem" AS category
          ON item.iot_category_id = category.name
      INNER JOIN "tabItem Group" AS cate_group
          ON category.item_group = cate_group.name
      INNER JOIN "tabUOM" AS unit
          ON category.stock_uom = unit.name
      GROUP BY
          category.name,
          cate_group.label,
          cate_group.name,
          unit.uom_name,
          unit.name
      ORDER BY
          cate_group.label;
      `,
        [cropId],
      );
      const formattedResult = res.map((item: any) => ({
        ...item,
        total_exp_quantity: parseFloat(item.total_exp_quantity),
        total_quantity: parseFloat(item.total_quantity),
        total_loss_quantity: parseFloat(item.total_loss_quantity),
        remain_quantity: parseFloat(item.remain_quantity),
      }));
      return formattedResult;
    } catch (error) {
      throw error;
    }
  }

  async getStatisticWorksheet(user: ICurrentUser, params: any) {
    try {
      const cropId = params.crop_id;
      const res = await ERPExecute(
        `
      with task_temp as (select task.name as task_id
              from "tabiot_farming_plan_task" as task
                      inner join
                  tabiot_farming_plan_state as plan_state
                  on
                      task.farming_plan_state = plan_state.name
                      inner join
                  tabiot_farming_plan as plan
                  on
                      plan_state.farming_plan = plan.name
                      inner join
                  tabiot_crop as crop
                  on
                      crop.name = plan.crop
              where crop.name = $1)
      select
        work_type.label as work_type_label,
        sum(work_sheet.exp_quantity) as total_exp_quantity,
        sum(work_sheet.quantity) as total_quantity,
        sum(work_sheet.cost) as cost,
        work_sheet.type as type
      from
        tabiot_farming_plan_task_worksheet as work_sheet
      inner join
        task_temp
      on
        work_sheet.task_id = task_temp.task_id
      inner join
        tabiot_task_work_type as work_type
      on
        work_sheet.work_type_id = work_type.name
      group by 
        work_type.name, work_sheet.type;
      `,
        [cropId],
      );
      const formattedResult = res.map((item: any) => ({
        ...item,
        total_exp_quantity: parseFloat(item.total_exp_quantity),
        total_quantity: parseFloat(item.total_quantity),
        cost: parseFloat(item.cost),
      }));
      return formattedResult;
    } catch (error) {
      throw error;
    }
  }

  async getCropManagementInfo(user: ICurrentUser, params: ICropManagementParams) {
    try {
      let filter = '';
      if (params.start_date) {
        filter += `AND crop.start_date >= '${params.start_date}'`;
      }
      if (params.end_date) {
        filter += `AND crop.end_date <= '${params.end_date}'`;
      }
      if (params.crop_name) {
        filter += `AND crop.label ILIKE '%${params.crop_name}%'`;
      }
      if (params.plant_name) {
        filter += `AND plant.label ILIKE '%${params.plant_name}%'`;
      }
      if (params.plant_id) {
        filter += `AND plant.name = '${params.plant_id}'`;
      }
      if (params.zone_name) {
        filter += `AND zone.label ILIKE '%${params.zone_name}%'`;
      }
      if (params.zone_id) {
        filter += `AND zone.name = '${params.zone_id}'`;
      }
      if (params.project_name) {
        filter += `AND project.label ILIKE '%${params.project_name}%'`;
      }
      if (params.status) {
        let parseStatus = JSON.parse(params.status);
        filter += `AND crop.status IN (${parseStatus.map((d: any) => `'${d}'`)})`;
      }
      if (params.is_template) {
        filter += `AND crop.is_template = ${params.is_template}`;
      }
      //pagination
      const page: number = params.page ? parseInt(params.page) : 1;
      const size: number = params.size ? parseInt(params.size) : 10000;

      let orderBySql = '';
      if (params.order_by) {
        orderBySql = `ORDER BY ${params.order_by}`;
      }
      //time filter
      // Handle date filters using moment.js
      const currentDate = moment().add(7, 'hours');

      let startOfWeek = moment(currentDate).startOf('isoWeek');
      let endOfWeek = moment(currentDate).endOf('isoWeek');

      let startOfMonth = moment(currentDate).startOf('month');
      let endOfMonth = moment(currentDate).endOf('month');

      let startOfYear = moment(currentDate).startOf('year');
      let endOfYear = moment(currentDate).endOf('year');

      let startOfNext3Months = moment(currentDate).add(1, 'months').startOf('month');
      let endOfNext3Months = moment(currentDate).add(3, 'months').endOf('month');

      switch (params.dateFilter) {
        case 'this_week':
          filter += `AND crop.start_date >= '${startOfWeek.toISOString()}' AND crop.start_date <= '${endOfWeek.toISOString()}'`;
          break;
        case 'this_month':
          filter += `AND crop.start_date >= '${startOfMonth.toISOString()}' AND crop.start_date <= '${endOfMonth.toISOString()}'`;
          break;
        case 'this_year':
          filter += `AND crop.start_date >= '${startOfYear.toISOString()}' AND crop.start_date <= '${endOfYear.toISOString()}'`;
          break;
        case 'next_3_months':
          filter += `AND crop.start_date >= '${startOfNext3Months.toISOString()}' AND crop.start_date <= '${endOfNext3Months.toISOString()}'`;
          break;
        default:
          // Handle other cases if necessary
          break;
      }

      const cropParticipantList = await cropParticipantsVerify({
        customerId: user.customer_id,
        userId: user.user_id,
        sections: user.sections,
      });
      let cropSQLCondition = 'AND FALSE';
      if (cropParticipantList.length > 0) {
        const cropNames = cropParticipantList.map((d: any) => `'${d.name}'`).join(',');
        cropSQLCondition = `AND crop.name IN (${cropNames})`;
      }

      if (user.user_type === UserType.SYSTEM_USER) {
        let cropList = await ERPExecute(
          `
          SELECT 
            crop.name,
            crop.label,
            crop.avatar,
            crop.is_template,
            plant.name AS plant_id,
            plant.label AS plant_name,
            zone.name AS zone_id,
            zone.label AS zone_name,
            project.name AS project_id,
            project.label AS project_name,
            crop.start_date,
            crop.end_date,
            crop.status,
            CONCAT(crop.name,crop.label,plant.label,zone.label,project.label) AS search_key
          FROM tabiot_crop AS crop
          LEFT JOIN tabiot_plant AS plant ON plant.name = crop.plant_id
          LEFT JOIN tabiot_zone AS zone ON zone.name = crop.zone_id
          LEFT JOIN tabiot_project AS project ON project.name = project_id
          WHERE TRUE
          AND crop.is_deleted = 0
          ${filter}
          ORDER BY crop.start_date DESC
            `,
          [],
        );
        return cropList;
      } else {
        /**
         * verify user
         */
        let cropList = await ERPExecute(
          `
          SELECT 
            crop.name,
            crop.label,
            crop.avatar,
            crop.is_template,
            plant.name AS plant_id,
            plant.label AS plant_name,
            zone.name AS zone_id,
            zone.label AS zone_name,
            project.name AS project_id,
            project.label AS project_name,
            crop.start_date,
            crop.end_date,
            crop.status,
            CONCAT(crop.name, crop.label, plant.label, zone.label, project.label) AS search_key
          FROM tabiot_crop AS crop
          LEFT JOIN tabiot_plant AS plant ON plant.name = crop.plant_id
          LEFT JOIN tabiot_zone AS zone ON zone.name = crop.zone_id
          LEFT JOIN tabiot_project AS project ON project.name = project_id
          WHERE (
            project.customer_id = '${user.customer_id}'
          OR
            zone.customer_id = '${user.customer_id}'
          )
          AND crop.is_deleted = 0
          ${cropSQLCondition}
          ${filter}
          GROUP BY
            crop.name,
            plant.name,
            zone.name,
            project.name
          ${orderBySql}
          LIMIT $1 OFFSET $2
            `,
          [size, (page - 1) * size],
        );

        let cropListCount = await ERPExecute(
          `
          SELECT 
            crop.name,
            crop.label,
            crop.avatar,
            plant.name AS plant_id,
            plant.label AS plant_name,
            zone.name AS zone_id,
            zone.label AS zone_name,
            project.name AS project_id,
            project.label AS project_name,
            crop.start_date,
            crop.end_date,
            crop.status,
            CONCAT(crop.name, crop.label, plant.label, zone.label, project.label) AS search_key
          FROM tabiot_crop AS crop
          LEFT JOIN tabiot_plant AS plant ON plant.name = crop.plant_id
          LEFT JOIN tabiot_zone AS zone ON zone.name = crop.zone_id
          LEFT JOIN tabiot_project AS project ON project.name = project_id
          WHERE (
            project.customer_id = '${user.customer_id}'
          OR
            zone.customer_id = '${user.customer_id}'
          )
          AND crop.is_deleted = 0
          ${cropSQLCondition}
          ${filter}
          GROUP BY
            crop.name,
            plant.name,
            zone.name,
            project.name
          ${orderBySql}
          
            `,
          [],
        );
        //list of
        const pagination = {
          pageNumber: page,
          pageSize: size,
          totalElements: cropListCount.length,
          totalPages: Math.ceil(cropListCount.length / size),
        };

        return {
          data: cropList,
          pagination: pagination,
        };
      }
    } catch (error) {
      throw error;
    }
  }

  async createCropAdmin(user: ICurrentUser, body: IIotCrop) {
    const response = await this.frappeService.generalCreate({
      doc_name: 'iot_crop',
      data: body,
    });
    return response;
  }

  async createCrop(user: ICurrentUser, body: IIotCrop) {
    try {
      // Validate tag if provided
      if (body.tag) {
        await this.validateTag(body.tag, user);
      }

      const zoneList = await ERPExecute(
        `
        SELECT zone.name
        FROM
            "tabiot_zone" as zone
        INNER JOIN
            "tabiot_customer" as customer
        ON
            zone.customer_id = customer.name
        WHERE
            customer.name = $1
    `,
        [user.customer_id],
      );
      const verify: any = zoneList.some(obj => obj.name === body.zone_id);
      if (verify) {
        // Check and format start_date
        if (body.start_date) {
          body.start_date = standardizeDateFormatToYYYY_MM_DD(body.start_date);
        }

        // Check and format end_date
        if (body.end_date) {
          body.end_date = standardizeDateFormatToYYYY_MM_DD(body.end_date);
        }
        const response: any = await this.frappeService.generalCreate({
          doc_name: 'iot_crop',
          data: body,
        });
        console.log('response', response);

        // Update crop tag in TypeORM database
        if (response.data && response.data.name) {
          await this.updateCropTagInTypeORM(response.data.name, body.tag || undefined);
        }

        //create iot_employee_in_crop
        let empInCropBody = {
          iot_crop: response.data.name,
          iot_customer_user: user.user_id,
        };
        const empInCrop = await this.frappeService.generalCreate({
          doc_name: 'iot_employee_in_crop',
          data: empInCropBody,
        });

        return response;
      } else {
        throw new HttpError(
          403,
          JSON.stringify({
            status: 403,
            message: "You don't have permission to create this iot_crop",
          }),
        );
      }
    } catch (error) {
      throw error;
    }
  }

  async updateCropAdmin(user: ICurrentUser, body: IIotCrop) {
    // Validate tag if provided (admin can use any tag)
    if (body.tag) {
      const tag = await this.tagRepository.findOne({
        where: { name: body.tag }
      });
      if (!tag) {
        throw new HttpError(404, `Tag with name ${body.tag} not found`);
      }
    }

    // Check and format start_date
    if (body.start_date) {
      body.start_date = standardizeDateFormatToYYYY_MM_DD(body.start_date);
    }

    // Check and format end_date
    if (body.end_date) {
      body.end_date = standardizeDateFormatToYYYY_MM_DD(body.end_date);
    }
    const response = await this.frappeService.generalUpdate({
      doc_name: 'iot_crop',
      name: body.name.toString(),
      data: body,
    });

    // Update crop tag in TypeORM database
    if (body.name) {
      await this.updateCropTagInTypeORM(body.name.toString(), body.tag || undefined);
    }

    return response;
  }

  async updateCrop(user: ICurrentUser, body: IIotCrop) {
    try {
      // Validate tag if provided
      if (body.tag) {
        await this.validateTag(body.tag, user);
      }

      const cropList: any[] = await this.otherService.getCropList(user);
      const zoneList: any[] = await ERPExecute(
        `
          SELECT
              name as zone_id
          FROM
              "tabiot_zone"
          WHERE
              customer_id = $1
      `,
        [user.customer_id],
      );
      const condition: any =
        cropList.some(obj => obj.name === body.name) && zoneList.some(obj => obj.zone_id === body.zone_id);

      //turn off verify in here cause already verify in get crop
      if (true) {
        // Check and format start_date
        if (body.start_date) {
          body.start_date = standardizeDateFormatToYYYY_MM_DD(body.start_date);
        }

        // Check and format end_date
        if (body.end_date) {
          body.end_date = standardizeDateFormatToYYYY_MM_DD(body.end_date);
        }
        const response = await this.frappeService.generalUpdate({
          doc_name: 'iot_crop',
          name: body.name.toString(),
          data: body,
        });

        // Update crop tag in TypeORM database
        if (body.name) {
          await this.updateCropTagInTypeORM(body.name.toString(), body.tag || undefined);
        }

        return response;
      }

      throw new HttpError(
        403,
        JSON.stringify({
          status: 403,
          message: "You don't have permission to update this iot_crop",
        }),
      );
    } catch (error) {
      throw error;
    }
  }

  async deleteCropAdmin(user: ICurrentUser, params: { name: string }) {
    const deleteRc: any = await this.frappeService.generalDelete({ name: params.name, doc_name: 'iot_crop' });
    return deleteRc;
  }
  async deleteCrop(user: ICurrentUser, params: { name: string }) {
    try {
      const cropList: any[] = await this.otherService.getCropList(user);
      const condition: any = cropList.some(obj => obj.name === params.name);
      if (condition) {
        const deleteRc: any = await this.frappeService.generalDelete({ name: params.name, doc_name: 'iot_crop' });
        return deleteRc;
      }
      throw new HttpError(
        403,
        JSON.stringify({
          status: 403,
          message: "You don't have permission to delete this iot_crop",
        }),
      );
    } catch (error) {
      throw error;
    }
  }

  async deleteCropAllResource(user: ICurrentUser, params: { name: string }) {
    try {
      const cropList: any[] = await this.otherService.getCropList(user);
      const condition: any = cropList.some(obj => obj.name === params.name);
      if (condition) {
        const res = await ERPExecute(`DELETE FROM tabiot_crop WHERE name = $1`, [params.name]);
        return res;
      }
      throw new HttpError(
        403,
        JSON.stringify({
          status: 403,
          message: "You don't have permission to delete this iot_crop",
        }),
      );
    } catch (error) {
      throw error;
    }
  }

  async getgetCurrentStateOfCrop(crop_id: string) {
    try {
      //today
      const currentDate = moment().format('YYYY-MM-DD');
      //get current state of crop base on start_date and end_date of iot_farming_plan_state
      const query = `
      SELECT
        state.name,
        state.label,
        state.start_date,
        state.end_date
      FROM tabiot_crop AS crop
      LEFT JOIN tabiot_farming_plan AS plan
      ON crop.name = plan.crop
      LEFT JOIN tabiot_farming_plan_state AS state
      ON plan.name = state.farming_plan
      WHERE crop.name = $1
      AND state.start_date <= $2
      AND state.end_date >= $2
      `;
      const res = await ERPExecute(query, [crop_id, currentDate]);
      return res;
    } catch (error) {
      throw error;
    }
  }
}
