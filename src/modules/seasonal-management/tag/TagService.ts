import { Service } from 'typedi';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { AppDataSource } from '@app/orm/dataSource';
import { IoTTag } from '@app/orm/entities/farmingPlan/tag/Tag';
import { HttpError } from 'routing-controllers';
import { FilterTuple, applyQueryFilters } from '@app/utils/helpers/queryHelpter';
import { ICurrentUser } from '@app/interfaces/ICurrentUser';
import { TagDto } from './TagDto';

@Service()
export class TagService {
  private tagRepository: Repository<IoTTag> = AppDataSource.getRepository(IoTTag);

  async listTags(
    user: ICurrentUser,
    page: number = 1,
    size: number = 10,
    filters?: FilterTuple[]
  ) {
    const skip = (page - 1) * size;
    const take = size;
    let qb = this.tagRepository.createQueryBuilder('tag')
      .where('tag.customer_id = :customer_id', { customer_id: user.customer_id });
    
    if (filters) qb = applyQueryFilters(qb, filters, 'tag');
    
    qb = qb.leftJoinAndSelect('tag.crops', 'crops')
      .orderBy('tag.creation', 'DESC')
      .skip(skip)
      .take(take);
    
    const [data, total] = await qb.getManyAndCount();
    return { data, page, size, total };
  }

  async getTagById(tagId: string, user: ICurrentUser): Promise<IoTTag> {
    const tag = await this.tagRepository.findOne({ 
      where: { name: tagId },
      relations: ['crops']
    });
    
    if (!tag || tag.customer_id !== user.customer_id) {
      throw new HttpError(404, `Tag with ID ${tagId} not found`);
    }
    
    return tag;
  }

  async createTag(user: ICurrentUser, dto: TagDto): Promise<IoTTag> {
    if (!dto.name) {
      dto.name = uuidv4();
    }
    
    // Set customer_id from current user
    dto.customer_id = user.customer_id;
    
    const entity = this.tagRepository.create({ ...dto });
    const saved = await this.tagRepository.save(entity);
    
    return await this.tagRepository.findOne({
      where: { name: saved.name },
      relations: ['crops'],
    }) as IoTTag;
  }

  async updateTag(tagId: string, dto: Partial<TagDto>, user: ICurrentUser): Promise<IoTTag> {
    const tag = await this.tagRepository.findOne({ 
      where: { name: tagId },
      relations: ['crops']
    });
    
    if (!tag || tag.customer_id !== user.customer_id) {
      throw new HttpError(404, `Tag with ID ${tagId} not found`);
    }
    
    // Don't allow changing customer_id
    delete dto.customer_id;
    
    Object.assign(tag, dto);
    const updated = await this.tagRepository.save(tag);
    
    return await this.tagRepository.findOne({
      where: { name: updated.name },
      relations: ['crops'],
    }) as IoTTag;
  }

  async deleteTag(tagId: string, user: ICurrentUser) {
    const tag = await this.tagRepository.findOne({ 
      where: { name: tagId },
      relations: ['crops']
    });
    
    if (!tag || tag.customer_id !== user.customer_id) {
      throw new HttpError(404, `Tag with ID ${tagId} not found`);
    }
    
    await this.tagRepository.remove(tag);
    return { success: true, message: `Tag with ID ${tagId} has been deleted` };
  }
}
