# Test Examples for VietPlants Environment Task Module

## 1. Create a Basic ENV_STOCK Task

```bash
curl -X POST http://localhost:3000/seasonal-management/farming-plan-task/env \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "stock-task-001",
    "farming_plan_state": "state-001",
    "task_type": "ENV_STOCK",
    "label": "Kho stock chính",
    "description": "Task quản lý kho stock",
    "start_date": "2024-01-01T00:00:00Z",
    "end_date": "2024-01-31T23:59:59Z"
  }'
```

## 2. Create a Basic ENV_STEAM_POT Task

```bash
curl -X POST http://localhost:3000/seasonal-management/farming-plan-task/env \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "steam-task-001",
    "farming_plan_state": "state-001", 
    "task_type": "ENV_STEAM_POT",
    "label": "<PERSON><PERSON>i hấp số 1",
    "description": "Task quản lý nồi hấp",
    "start_date": "2024-01-01T00:00:00Z",
    "end_date": "2024-01-31T23:59:59Z"
  }'
```

## 3. Create an ENV_POUR Task with Task Transfers

```bash
curl -X POST http://localhost:3000/seasonal-management/farming-plan-task/env \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "pour-task-001",
    "farming_plan_state": "state-001",
    "task_type": "ENV_POUR",
    "label": "Chiết rót từ stock và nồi hấp",
    "description": "Task chiết rót kết hợp từ kho và nồi hấp",
    "start_date": "2024-01-02T00:00:00Z",
    "end_date": "2024-01-02T23:59:59Z",
    "task_transfers": [
      {
        "source_task_id": "stock-task-001",
        "source_task_type": "ENV_STOCK",
        "description": "Lấy nguyên liệu từ kho stock"
      },
      {
        "source_task_id": "steam-task-001",
        "source_task_type": "ENV_STEAM_POT", 
        "description": "Lấy sản phẩm từ nồi hấp"
      }
    ]
  }'
```

## 4. Bulk Create Multiple Tasks

```bash
curl -X POST http://localhost:3000/seasonal-management/farming-plan-task/env/bulk \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "tasks": [
      {
        "name": "stock-task-002",
        "farming_plan_state": "state-001",
        "task_type": "ENV_STOCK",
        "label": "Kho stock phụ"
      },
      {
        "name": "steam-task-002",
        "farming_plan_state": "state-001",
        "task_type": "ENV_STEAM_POT",
        "label": "Nồi hấp số 2"
      },
      {
        "name": "prep-task-001",
        "farming_plan_state": "state-001",
        "task_type": "ENV_PREPARATION",
        "label": "Chuẩn bị nguyên liệu"
      }
    ]
  }'
```

## 5. Get All Tasks with Filtering

```bash
# Get all tasks
curl -X GET "http://localhost:3000/seasonal-management/farming-plan-task/env" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get tasks by state
curl -X GET "http://localhost:3000/seasonal-management/farming-plan-task/env?stateId=state-001" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get tasks by type
curl -X GET "http://localhost:3000/seasonal-management/farming-plan-task/env?taskType=ENV_POUR" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 6. Get Available Source Tasks for ENV_POUR

```bash
curl -X GET "http://localhost:3000/seasonal-management/farming-plan-task/env/available-source-tasks?stateId=state-001" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 7. Get Task by ID

```bash
curl -X GET "http://localhost:3000/seasonal-management/farming-plan-task/env/pour-task-001" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 8. Update a Task

```bash
curl -X PUT http://localhost:3000/seasonal-management/farming-plan-task/env/pour-task-001 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "label": "Chiết rót cập nhật",
    "description": "Mô tả đã được cập nhật",
    "status": "In progress"
  }'
```

## 9. Get Task Transfers

```bash
curl -X GET "http://localhost:3000/seasonal-management/farming-plan-task/env/pour-task-001/transfers" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 10. Delete a Single Task

```bash
curl -X DELETE "http://localhost:3000/seasonal-management/farming-plan-task/env/pour-task-001" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 11. Bulk Delete Tasks

```bash
curl -X DELETE http://localhost:3000/seasonal-management/farming-plan-task/env/bulk \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "task_ids": ["stock-task-002", "steam-task-002", "prep-task-001"],
    "reason": "Cleanup test tasks"
  }'
```

## Expected Response Format

### Success Response
```json
{
  "success": true,
  "data": {
    "name": "task-id",
    "label": "Task Label",
    "task_type": "ENV_POUR",
    "status": "Not started",
    "created": "2024-01-01T00:00:00Z",
    "modified": "2024-01-01T00:00:00Z"
  }
}
```

### Error Response
```json
{
  "error": "Error message",
  "details": "Detailed error information"
}
```

## Notes

1. Replace `YOUR_TOKEN` with actual authentication token
2. Replace `state-001` with actual farming plan state ID
3. Ensure source tasks exist before creating ENV_POUR tasks with transfers
4. Task names must be unique within the system
5. ENV_POUR tasks can only link to ENV_STOCK and ENV_STEAM_POT task types
