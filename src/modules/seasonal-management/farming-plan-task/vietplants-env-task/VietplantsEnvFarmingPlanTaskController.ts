import { Container } from 'typedi';
import {
  Authorized,
  Body,
  CurrentUser,
  Delete,
  Get,
  HttpError,
  JsonController,
  Param,
  Post,
  Put,
  QueryParam,
  QueryParams,
} from 'routing-controllers';
import { UserType } from '@app/utils/enums/usertype';
import { ICurrentUser } from '@app/interfaces';
import Logger from '@app/loaders/logger';
import { FilterTuple } from '@app/utils/helpers/queryHelpter';
import { UpdateFarmingPlanTaskDto, AssignFarmingPlanTaskDtoArray, TaskManagementInfoDto } from '../task/FarmingPlanTask.dto';
import { CreateTaskItemTransferDto, BulkTaskItemTransferDto, MultiParentTaskItemTransferDto, TaskTreeQueryDto } from '../vietplants-item-transfer/TaskItemTransfer.dto';
import { CreateFarmingPlanTaskDto, BulkCreateTaskDto, BulkDeleteTaskDto, ApproveEnvPourTaskDto } from './VietplantsEnvFarmingPlanTask.dto';
import { TaskType } from '@app/orm/entities/farmingPlan/template/FarmingPlanTaskTemplate';
import { FarmingPlanTaskService } from './VietplantsEnvFarmingPlanTaskService';
import { TaskItemTransferService } from '../vietplants-item-transfer/TaskItemTransferService';

@JsonController('/seasonal-management/farming-plan-task/env')
export class FarmingPlanTaskController {
  private readonly farmingPlanTaskService: FarmingPlanTaskService;
  private readonly taskItemTransferService: TaskItemTransferService;

  constructor() {
    this.farmingPlanTaskService = Container.get(FarmingPlanTaskService);
    this.taskItemTransferService = Container.get(TaskItemTransferService);
  }

  /**
   * Get task management info with filtering and pagination
   */
  @Get('/task-management-info')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getTaskManagementInfoForEnv(
    @CurrentUser() user: ICurrentUser,
    @QueryParam('page') page: number = 1,
    @QueryParam('size') size: number = 100,
    @QueryParam('filters') filters?: string,
    @QueryParam('stateId') stateId?: string,
    @QueryParam('templateId') templateId?: string,
    @QueryParam('status') status?: string,
    @QueryParam('assignedTo') assignedTo?: string,
    @QueryParam('taskType') taskType?: string,
    @QueryParam('orderBy') orderBy?: string
  ) {
    try {
      let parsedFilters: FilterTuple[] = [];
      if (filters) {
        try {
          parsedFilters = JSON.parse(filters);
          //only filter task type include Env
          parsedFilters.push(['task_type', 'ILIKE', '%ENV%']);
          console.log("parsedFilters", filters, parsedFilters);
        } catch (parseError) {
          Logger.error('Failed to parse filters:', { filters, error: parseError });
          throw new Error('Invalid filters format');
        }
      }

      return await this.farmingPlanTaskService.getTaskManagementInfo(
        user,
        page,
        size,
        parsedFilters,
        stateId,
        templateId,
        status,
        assignedTo,
        taskType,
        orderBy
      );
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Get a task by ID
   */
  @Get('/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getTaskById(
    @CurrentUser() user: ICurrentUser,
    @Param('id') taskId: string
  ) {
    try {
      return await this.farmingPlanTaskService.getTaskById(user, taskId);
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Create a new task
   * Automatically handles ENV_POUR tasks with task transfers based on task_type
   */
  @Post('/')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async createTask(
    @CurrentUser() user: ICurrentUser,
    @Body() createData: CreateFarmingPlanTaskDto
  ) {
    try {
      // Check if this is an ENV_POUR task with transfers
      if (createData.task_type === TaskType.ENV_POUR && createData.task_transfers && createData.task_transfers.length > 0) {
        return await this.farmingPlanTaskService.createEnvPourTask(user, createData as any);
      }

      // Regular task creation
      return await this.farmingPlanTaskService.createTask(user, createData as any);
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Bulk create tasks
   */
  @Post('/bulk')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async bulkCreateTasks(
    @CurrentUser() user: ICurrentUser,
    @Body() bulkData: BulkCreateTaskDto
  ) {
    try {
      return await this.farmingPlanTaskService.bulkCreateTasks(user, bulkData);
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Update a task with partial data
   * Any field included in the request body will be updated
   */
  @Put('/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async updateTask(
    @CurrentUser() user: ICurrentUser,
    @Param('id') taskId: string,
    @Body() updateData: UpdateFarmingPlanTaskDto
  ) {
    try {
      return await this.farmingPlanTaskService.updateTask(user, taskId, updateData);
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Delete a task by ID
   */
  @Delete('/:id')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async deleteTask(
    @CurrentUser() user: ICurrentUser,
    @Param('id') taskId: string
  ) {
    try {
      return await this.farmingPlanTaskService.deleteTask(user, taskId);
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Bulk delete tasks
   */
  @Delete('/bulk')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async bulkDeleteTasks(
    @CurrentUser() user: ICurrentUser,
    @Body() bulkData: BulkDeleteTaskDto
  ) {
    try {
      return await this.farmingPlanTaskService.bulkDeleteTasks(user, bulkData);
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Get available source tasks for ENV_POUR task linking
   * Returns ENV_STOCK and ENV_STEAM_POT tasks that can be linked
   */
  @Get('/available-source-tasks')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getAvailableSourceTasks(
    @CurrentUser() user: ICurrentUser,
    @QueryParam('stateId') stateId?: string,
    @QueryParam('departmentId') departmentId?: string
  ) {
    try {
      return await this.farmingPlanTaskService.getAvailableSourceTasks(user, stateId, departmentId);
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Get task transfers for a specific task
   */
  @Get('/:id/transfers')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getTaskTransfers(
    @CurrentUser() user: ICurrentUser,
    @Param('id') taskId: string
  ) {
    try {
      return await this.taskItemTransferService.getIncomingTransfers(user, taskId);
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Get tasks with "In Progress" status
   */
  @Get('/status/in-progress')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async getInProgressTasks(
    @CurrentUser() user: ICurrentUser,
    @QueryParam('page') page: number = 1,
    @QueryParam('size') size: number = 10,
    @QueryParam('taskType') taskType?: string
  ) {
    try {
      return await this.farmingPlanTaskService.getInProgressTasks(user, page, size, taskType);
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }

  /**
   * Approve ENV_POUR task and change status to completed
   * Only for ENV_POUR task type
   */
  @Post('/approve-env-pour')
  @Authorized([UserType.SYSTEM_USER, UserType.VIIS_IOT_USER])
  async approveEnvPourTask(
    @CurrentUser() user: ICurrentUser,
    @Body() approvalData: ApproveEnvPourTaskDto
  ) {
    try {
      return await this.farmingPlanTaskService.approveEnvPourTask(user, approvalData);
    } catch (error) {
      Logger.error(error);
      throw error;
    }
  }


}
