# Unified Endpoint Design for VietPlants Environment Tasks

## Overview

The VietPlants Environment Task module now uses a **unified endpoint approach** where all task creation goes through a single `POST /` endpoint, with logic determined by the `task_type` field in the request body.

## Design Philosophy

Instead of having separate endpoints for different task types:
- ❌ `POST /env-pour` (separate endpoint)
- ✅ `POST /` (unified endpoint with smart routing)

## How It Works

### Single Endpoint with Smart Logic

```typescript
@Post('/')
async createTask(@Body() createData: CreateFarmingPlanTaskDto) {
  // Check if this is an ENV_POUR task with transfers
  if (createData.task_type === TaskType.ENV_POUR && createData.task_transfers?.length > 0) {
    return await this.farmingPlanTaskService.createEnvPourTask(user, createData);
  }
  
  // Regular task creation for all other types
  return await this.farmingPlanTaskService.createTask(user, createData);
}
```

### Request Body Determines Behavior

#### Regular Task Creation
```json
{
  "name": "stock-task-001",
  "farming_plan_state": "state-001",
  "task_type": "ENV_STOCK",
  "label": "Kho stock chính"
}
```

#### ENV_POUR Task with Transfers
```json
{
  "name": "pour-task-001", 
  "farming_plan_state": "state-001",
  "task_type": "ENV_POUR",
  "label": "Chiết rót từ stock và nồi hấp",
  "task_transfers": [
    {
      "source_task_id": "stock-task-001",
      "source_task_type": "ENV_STOCK",
      "description": "Lấy từ kho stock"
    },
    {
      "source_task_id": "steam-task-001",
      "source_task_type": "ENV_STEAM_POT", 
      "description": "Lấy từ nồi hấp"
    }
  ]
}
```

## Benefits

### 1. **Simplified API Surface**
- Single endpoint to remember: `POST /seasonal-management/farming-plan-task/env`
- No need to know which specific endpoint to use for different task types

### 2. **Consistent Interface**
- All task creation follows the same pattern
- Same authentication, validation, and error handling

### 3. **Extensible Design**
- Easy to add new task types without creating new endpoints
- Logic can be extended based on task_type or other fields

### 4. **Better Developer Experience**
- Intuitive: "I want to create a task" → use the create task endpoint
- Self-documenting: the request body shows what type of task is being created

## Implementation Details

### DTO Structure
```typescript
export class CreateFarmingPlanTaskDto {
  name?: string;
  farming_plan_state: string;
  task_type?: TaskType;
  label?: string;
  description?: string;
  
  // Optional field for ENV_POUR tasks
  task_transfers?: TaskTransferDto[];
}

export class TaskTransferDto {
  source_task_id: string;
  source_task_type: TaskType; // ENV_STOCK or ENV_STEAM_POT
  description?: string;
}
```

### Service Layer Logic
```typescript
async createEnvPourTask(user: ICurrentUser, createData: CreateFarmingPlanTaskDto & { task_transfers?: TaskTransferDto[] }) {
  // Validate task type
  if (createData.task_type !== 'ENV_POUR') {
    throw new Error('This method is only for ENV_POUR tasks');
  }

  // Validate task transfers if provided
  if (createData.task_transfers?.length > 0) {
    await this.validateTaskTransfers(user, createData.task_transfers);
  }

  // Create the main task
  const task = await this.createTask(user, createData);

  // Create task transfers if provided
  if (createData.task_transfers?.length > 0) {
    await this.createTaskTransfers(user, task.name, createData.task_transfers);
  }

  return await this.getTaskById(user, task.name);
}
```

## Usage Examples

### Creating Different Task Types

All use the same endpoint but with different request bodies:

```bash
# ENV_STOCK task
curl -X POST http://localhost:3000/seasonal-management/farming-plan-task/env \
  -d '{"task_type": "ENV_STOCK", "label": "Stock task"}'

# ENV_STEAM_POT task  
curl -X POST http://localhost:3000/seasonal-management/farming-plan-task/env \
  -d '{"task_type": "ENV_STEAM_POT", "label": "Steam pot task"}'

# ENV_POUR task with transfers
curl -X POST http://localhost:3000/seasonal-management/farming-plan-task/env \
  -d '{"task_type": "ENV_POUR", "task_transfers": [...]}'
```

## Migration Notes

### Before (Multiple Endpoints)
```
POST /seasonal-management/farming-plan-task/env          # Regular tasks
POST /seasonal-management/farming-plan-task/env/env-pour # ENV_POUR tasks
```

### After (Unified Endpoint)
```
POST /seasonal-management/farming-plan-task/env          # All tasks
```

The system automatically routes to the appropriate logic based on the request content.

## Validation Rules

1. **task_type** field determines the processing logic
2. **task_transfers** field is only processed for ENV_POUR tasks
3. Source tasks in transfers must exist and be of type ENV_STOCK or ENV_STEAM_POT
4. All existing validation rules still apply

This unified approach provides a cleaner, more maintainable API while preserving all the specialized functionality for different task types.
