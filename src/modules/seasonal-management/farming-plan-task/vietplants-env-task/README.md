# VietPlants Environment Task Module

This module provides comprehensive CRUD operations for farming plan tasks in the VietPlants environment, with special support for ENV_POUR tasks that can link to ENV_STOCK and ENV_STEAM_POT tasks.

## Features

### 1. Full CRUD Operations
- **Create**: Create new farming plan tasks
- **Read**: Get tasks with filtering and pagination
- **Update**: Update existing tasks
- **Delete**: Delete single or multiple tasks

### 2. Special ENV_POUR Task Support
ENV_POUR tasks can be linked to source tasks (ENV_STOCK and ENV_STEAM_POT) to establish task relationships.

### 3. Task Linking
- Link ENV_POUR tasks to ENV_STOCK tasks (stock sources)
- Link ENV_POUR tasks to ENV_STEAM_POT tasks (steaming pot sources)
- Maintain task transfer relationships

## API Endpoints

### Basic CRUD Operations

#### Create Task
```
POST /seasonal-management/farming-plan-task/env
```

#### Create Task (including ENV_POUR with Links)
```
POST /seasonal-management/farming-plan-task/env
```
*Note: ENV_POUR tasks with task_transfers are automatically handled by the same endpoint*

#### Bulk Create Tasks
```
POST /seasonal-management/farming-plan-task/env/bulk
```

#### Get Tasks
```
GET /seasonal-management/farming-plan-task/env
```

#### Get Task by ID
```
GET /seasonal-management/farming-plan-task/env/:id
```

#### Update Task
```
PUT /seasonal-management/farming-plan-task/env/:id
```

#### Delete Task
```
DELETE /seasonal-management/farming-plan-task/env/:id
```

#### Bulk Delete Tasks
```
DELETE /seasonal-management/farming-plan-task/env/bulk
```

### Task Linking Operations

#### Get Available Source Tasks
```
GET /seasonal-management/farming-plan-task/env/available-source-tasks
```

#### Get Task Transfers
```
GET /seasonal-management/farming-plan-task/env/:id/transfers
```

## Usage Examples

### Creating an ENV_POUR Task with Links

```typescript
const envPourTask = {
  name: "pour-task-001",
  farming_plan_state: "state-001",
  task_type: "ENV_POUR",
  label: "Chiết rót từ stock và nồi hấp",
  task_transfers: [
    {
      source_task_id: "stock-task-001",
      source_task_type: "ENV_STOCK",
      description: "Lấy từ kho stock"
    },
    {
      source_task_id: "steam-task-001", 
      source_task_type: "ENV_STEAM_POT",
      description: "Lấy từ nồi hấp"
    }
  ]
};

// POST /seasonal-management/farming-plan-task/env
```

### Bulk Creating Tasks

```typescript
const bulkTasks = {
  tasks: [
    {
      name: "task-001",
      farming_plan_state: "state-001",
      task_type: "ENV_STOCK",
      label: "Task 1"
    },
    {
      name: "task-002", 
      farming_plan_state: "state-001",
      task_type: "ENV_STEAM_POT",
      label: "Task 2"
    }
  ]
};

// POST /seasonal-management/farming-plan-task/env/bulk
```

### Bulk Deleting Tasks

```typescript
const bulkDelete = {
  task_ids: ["task-001", "task-002", "task-003"],
  reason: "Cleanup old tasks"
};

// DELETE /seasonal-management/farming-plan-task/env/bulk
```

## Task Types Supported

- **ENV_STOCK**: Stock/inventory tasks
- **ENV_STEAM_POT**: Steam pot tasks  
- **ENV_POUR**: Pour/transfer tasks (can link to ENV_STOCK and ENV_STEAM_POT)
- **ENV_PREPARATION**: Preparation tasks
- And other environment task types

## Task Transfer Concept

For ENV_POUR tasks, the `task_transfers` field allows linking to source tasks:

- **source_task_id**: ID of the source task
- **source_task_type**: Must be ENV_STOCK or ENV_STEAM_POT
- **description**: Optional description of the transfer relationship

This creates a logical link between tasks without transferring specific item quantities (for item quantity transfers, use the TaskItemTransferService).

## Error Handling

The module includes comprehensive error handling:
- Validation of task types for transfers
- Verification of source task existence
- Type checking for task relationships
- Proper error messages and logging

## Dependencies

- TypeORM for database operations
- class-validator for DTO validation
- routing-controllers for API endpoints
- typedi for dependency injection
