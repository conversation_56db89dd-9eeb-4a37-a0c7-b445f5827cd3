import Container, { Service } from 'typedi';
import { HttpError } from 'routing-controllers';
import { ICurrentUser } from '@app/interfaces';
import { v4 as uuidv4 } from 'uuid';
import moment from 'moment';
import { Logger } from '@app/loaders/logger';
import { TaskItemService } from '@app/modules/farming-plan/task/task-item/TaskItemService';
import { TaskProductionService } from '@app/modules/farming-plan/task/task-production/TaskProductionService';
import { StockEntryService } from '@app/modules/stock-v3/stock-entry/StockEntryService';
import { CropService } from '@app/modules/crop-manage/crop/CropService';
import { TaskSubcultureWorkflowDto, TaskItemDto, ProductionDto } from './TaskSubculture.dto';
import { ExecuteWorkDto, TaskItemWithOriginDto, TaskProductionItemDto } from './TaskSubcultureWorkExecution.dto';
import { ERPExecute, ERPExecuteWithTransaction } from '@app/loaders/pglib/PGDB';
import { AppDataSource } from '@app/orm/dataSource';
import { FarmingPlanTask, TaskStatus } from '@app/orm/entities/farmingPlan/FarmingPlanTask';
import { WarehouseItemTaskUsed } from '@app/orm/entities/farmingPlan/taskItem/WarehouseItemTaskUsed';
import { ProductionQuantity } from '@app/orm/entities/farmingPlan/taskItem/ProductionQuantity';
import { TaskItemTransfer } from '@app/orm/entities/farmingPlan/taskItem/TaskItemTransfer';
import { TaskItemTransferService } from '../vietplants-item-transfer/TaskItemTransferService';
import { FarmingPlanTaskService } from '../task/FarmingPlanTaskService';
import { LabelPrintManagementService } from '../vietplants-label-print-management/LabelPrintManagementService';

// Constants for warehouse names
const WORK_IN_PROGRESS_WAREHOUSE = 'Work In Progress - V';

/**
 * Service for managing the task subculture workflow
 * This includes material tracking and production tracking for tasks
 */
@Service()
export class TaskSubcultureService {
  private taskItemService: TaskItemService;
  private taskProductionService: TaskProductionService;
  private stockEntryService: StockEntryService;
  private cropService: CropService;
  private taskItemTransferService: TaskItemTransferService;
  private farmingPlanTaskService: FarmingPlanTaskService;
  private labelPrintManagementService: LabelPrintManagementService;
  constructor() {
    this.cropService = Container.get(CropService);
    this.taskItemTransferService = Container.get(TaskItemTransferService);
    this.farmingPlanTaskService = Container.get(FarmingPlanTaskService);
    this.taskItemService = Container.get(TaskItemService);
    this.taskProductionService = Container.get(TaskProductionService);
    this.stockEntryService = Container.get(StockEntryService);
    this.labelPrintManagementService = Container.get(LabelPrintManagementService);
  }

  /**
   * Executes the task subculture workflow:
   * 1. Creates stock entries for material and production transactions
   * 2. Updates task items (warehouse_item_task_used) based on stock entries
   * 3. Updates production quantities based on stock entries
   * 4. Updates totals and tracking information
   * 
   * @param user Current user
   * @param workflowData Task subculture workflow data
   * @returns Result of the workflow execution
   */
  /**
  * Executes the task subculture workflow:
  * 1. Creates stock entries for material and production transactions within a transaction
  * 2. Updates task items and production quantities only if all stock entries succeed
  * 3. Updates totals and tracking information
  * 
  * @param user Current user
  * @param workflowData Task subculture workflow data
  * @returns Result of the workflow execution
  */
  async executeConfirmWorkflow(
    user: ICurrentUser,
    workflowData: TaskSubcultureWorkflowDto
  ): Promise<any> {
    // Track successful operations for potential manual cleanup guidance
    const completedOperations: { type: string; ids: string[] }[] = [];

    try {
      // Direct database check for task processing status using repository
      const taskRepository = AppDataSource.getRepository(FarmingPlanTask);

      // Check if task exists and if it has been processed
      const existingTask = await taskRepository.findOne({
        where: { name: workflowData.task_id }
      });

      if (!existingTask) {
        throw new HttpError(404, `Task with ID ${workflowData.task_id} not found`);
      }

      // First check: based on the stock_entries_processed flag
      if (existingTask.stock_entries_processed) {
        Logger.info('Task already has stock entries processed (flag check), skipping duplicate transaction', {
          task_id: workflowData.task_id
        });
        return {
          success: false,
          alreadyProcessed: true,
          message: 'Công việc này đã được xử lý, không thể thực hiện lại'
        };
      }

      // Validate task exists and user has access (this also checks permissions)
      const task = await this.validateTaskAccess(user, workflowData.task_id);
      if (!task) {
        throw new HttpError(404, `Task ${workflowData.task_id} not found or access denied`);
      }

      // Get crop information for the task
      const cropInfo = await this.getCropInfoForTask(user, workflowData.task_id);
      if (!cropInfo) {
        throw new HttpError(404, `Could not determine crop for task ${workflowData.task_id}`);
      }

      // Validate warehouses before proceeding with stock transactions
      const isValidWarehouses = workflowData.source_warehouse && workflowData.target_warehouse;
      if (!isValidWarehouses) {
        Logger.warn('Skipping stock transactions - invalid warehouses', {
          source_warehouse: workflowData.source_warehouse,
          target_warehouse: workflowData.target_warehouse
        });
        throw new HttpError(400, 'Invalid warehouses specified. Source and target warehouses are required.');
      }

      // Prepare data for processing
      let itemsWithOrigin: TaskItemDto[] = [];
      let stockEntryResults: any[] = [];

      // Begin transaction processing for stock entries
      try {
        // Step 1: Get all incoming transfers for the current task
        const existingTransfers = await this.taskItemTransferService.getIncomingTransfers(user, workflowData.task_id);
        Logger.debug('Found existing transfers', { count: existingTransfers.length, taskId: workflowData.task_id });

        // Prepare items with origin
        if (existingTransfers.length > 0) {
          // Create a map of item_id to its transfer info
          const transferMap = new Map<string, { sourceTaskId: string, quantity: number }>();

          for (const transfer of existingTransfers) {
            transferMap.set(transfer.item_id, {
              sourceTaskId: transfer.source_task_id,
              quantity: transfer.quantity
            });
          }

          // Filter items that have existing transfers
          if (workflowData.task_items?.length > 0) {
            itemsWithOrigin = workflowData.task_items.filter(item => transferMap.has(item.iot_category_id));

            Logger.debug('Items with existing transfers', {
              itemCount: itemsWithOrigin.length,
              itemIds: itemsWithOrigin.map(i => i.iot_category_id)
            });
          }
        }

        // Step 2: Execute all stock entries within a transaction to ensure atomicity
        await AppDataSource.transaction(async transactionalEntityManager => {
          // Part 1: Material Transfer and Issue for items with origin
          if (itemsWithOrigin.length > 0 && isValidWarehouses) {
            // 1. Material Transfer from inventory to seasonal warehouse
            const materialTransferResult = await this.createStockEntry(user, {
              purpose: 'Material Transfer',
              items: itemsWithOrigin.map(item => ({
                item_code: item.iot_category_id,
                qty: item.quantity,
                uom: item.active_uom || '',
                conversion_factor: item.active_conversion_factor || 1,
                s_warehouse: workflowData.source_warehouse || '',
                t_warehouse: WORK_IN_PROGRESS_WAREHOUSE
              })),
              description: `Chuyển vật tư vào công việc "${task.name}" của mùa vụ "${cropInfo.crop_name}"`,
              iot_crop: cropInfo.crop_id,
              iot_farming_plan_task: workflowData.task_id
            });

            if (!materialTransferResult?.success) {
              Logger.error('Material transfer failed', materialTransferResult);
              throw new Error(`Material transfer failed: ${materialTransferResult?.message || 'Unknown error'}`);
            }

            stockEntryResults.push(materialTransferResult);
            completedOperations.push({ type: 'material_transfer', ids: [materialTransferResult.data?.name] });

            // 2. Material Issue from seasonal warehouse
            const materialIssueResult = await this.createStockEntry(user, {
              purpose: 'Material Issue',
              items: itemsWithOrigin.map(item => ({
                item_code: item.iot_category_id,
                qty: item.quantity,
                uom: item.active_uom || '',
                conversion_factor: item.active_conversion_factor || 1,
                s_warehouse: WORK_IN_PROGRESS_WAREHOUSE,
                t_warehouse: ''
              })),
              description: `Xuất sử dụng vật tư cho công việc "${task.label}" của mùa vụ "${cropInfo.crop_name}"`,
              iot_crop: cropInfo.crop_id,
              iot_farming_plan_task: workflowData.task_id
            });

            if (!materialIssueResult?.success) {
              Logger.error('Material issue failed', materialIssueResult);
              throw new Error(`Material issue failed: ${materialIssueResult?.message || 'Unknown error'}`);
            }

            stockEntryResults.push(materialIssueResult);
            completedOperations.push({ type: 'material_issue', ids: [materialIssueResult.data?.name] });

            Logger.debug('Processed stock entries for items with origin', {
              itemCount: itemsWithOrigin.length,
              stockEntryCount: stockEntryResults.length
            });
          }

          // Part 2: Material Receipt and Transfer for production
          if (workflowData.task_production?.length > 0 && isValidWarehouses) {
            // 3. Material Receipt for production into seasonal warehouse
            const materialReceiptResult = await this.createStockEntry(user, {
              purpose: 'Material Receipt',
              items: workflowData.task_production.map(item => ({
                item_code: item.product_id,
                qty: item.quantity,
                uom: item.active_uom || '',
                conversion_factor: item.active_conversion_factor || 1,
                s_warehouse: '',
                t_warehouse: WORK_IN_PROGRESS_WAREHOUSE,
                basic_rate: item.basic_rate || 10000 // Default price if not provided
              })),
              description: `Nhập sản lượng từ công việc "${task.label}" của mùa vụ "${cropInfo.crop_name}"`,
              iot_crop: cropInfo.crop_id,
              iot_farming_plan_task: workflowData.task_id
            });

            if (!materialReceiptResult?.success) {
              Logger.error('Material receipt failed', materialReceiptResult);
              throw new Error(`Material receipt failed: ${materialReceiptResult?.message || 'Unknown error'}`);
            }

            stockEntryResults.push(materialReceiptResult);
            completedOperations.push({ type: 'material_receipt', ids: [materialReceiptResult.data?.name] });

            // 4. Material Transfer for production from seasonal warehouse to finished goods
            const productionTransferResult = await this.createStockEntry(user, {
              purpose: 'Material Transfer',
              items: workflowData.task_production.map(item => ({
                item_code: item.product_id,
                qty: item.quantity,
                uom: item.active_uom || '',
                conversion_factor: item.active_conversion_factor || 1,
                s_warehouse: WORK_IN_PROGRESS_WAREHOUSE,
                t_warehouse: workflowData.target_warehouse || 'Finished Goods - V'
              })),
              description: `Chuyển sản lượng từ công việc "${task.label}" vào kho sản lượng`,
              iot_crop: cropInfo.crop_id,
              iot_farming_plan_task: workflowData.task_id
            });

            if (!productionTransferResult?.success) {
              Logger.error('Production transfer failed', productionTransferResult);
              throw new Error(`Production transfer failed: ${productionTransferResult?.message || 'Unknown error'}`);
            }

            stockEntryResults.push(productionTransferResult);
            completedOperations.push({ type: 'production_transfer', ids: [productionTransferResult.data?.name] });

            Logger.debug('Processed material receipt and transfer for production items', {
              itemCount: workflowData.task_production.length,
              stockEntryCount: stockEntryResults.length - 2 // Subtract previous entries
            });
          }
        });

        // All stock entries were successful at this point
        Logger.info('All stock entries processed successfully', {
          taskId: workflowData.task_id,
          stockEntryCount: stockEntryResults.length
        });

        // Step 3: Process task items (only if stock entries succeeded)
        let taskItemResult = { success: true, message: 'No task items processed' };

        if (stockEntryResults.length > 0 && itemsWithOrigin.length > 0) {
          // Update quantity and issued_quantity
          const updatedTaskItems = itemsWithOrigin.map((item: TaskItemDto) => ({
            ...item,
            quantity: item.quantity,
            issued_quantity: item.quantity
          }));

          Logger.debug('Processing updated task items after warehouse transactions', {
            count: updatedTaskItems.length,
            items: updatedTaskItems.map((i: TaskItemDto) => ({
              name: i.name,
              quantity: i.quantity,
              issued: i.issued_quantity
            }))
          });

          taskItemResult = await this.processTaskItems(user, updatedTaskItems);
          completedOperations.push({ type: 'task_items_update', ids: updatedTaskItems.map(i => i.name) });

          // Update the TaskItemTransfer records with the confirmed quantities
          try {
            // Get existing transfers for items that were confirmed
            const transferRepo = AppDataSource.getRepository(TaskItemTransfer);

            for (const item of itemsWithOrigin) {
              // Find transfers where this task is the target and the item matches
              const transfers = await transferRepo.find({
                where: {
                  target_task_id: workflowData.task_id,
                  item_id: item.iot_category_id
                }
              });

              if (transfers.length > 0) {
                Logger.debug('Updating TaskItemTransfer quantities after confirmation', {
                  taskId: workflowData.task_id,
                  itemId: item.iot_category_id,
                  confirmedQuantity: item.quantity,
                  transferCount: transfers.length
                });

                // Update each transfer with the confirmed quantity
                for (const transfer of transfers) {
                  transfer.quantity = item.quantity;
                  transfer.status = 'Completed';
                  await transferRepo.save(transfer);
                }

                completedOperations.push({
                  type: 'transfer_quantity_update',
                  ids: transfers.map(t => t.name)
                });
              }
            }
          } catch (transferError) {
            Logger.error('Error updating transfer quantities after confirmation:', transferError);
            // Don't throw here - we want to continue with the rest of the process even if this fails
          }
        }

        // Step 4: Process production quantities (only if stock entries succeeded)
        let productionResult = { success: true, message: 'No production items processed' };

        if (stockEntryResults.length > 0 && workflowData.task_production?.length > 0 && isValidWarehouses) {
          // Update quantity and finished_quantity
          const updatedProductionItems = workflowData.task_production.map((item: ProductionDto) => ({
            ...item,
            quantity: item.quantity,
            finished_quantity: item.quantity
          }));

          Logger.debug('Processing updated production items after warehouse transactions');
          console.log({
            count: updatedProductionItems.length,
            items: updatedProductionItems.map((i: ProductionDto) => ({
              name: i.name,
              product_id: i.product_id,
              exp_quantity: i.exp_quantity,
              quantity: i.quantity,
              finished: i.finished_quantity
            }))
          })

          productionResult = await this.processProductionItems(user, updatedProductionItems);
          completedOperations.push({ type: 'production_update', ids: updatedProductionItems.map(i => i.name!) });
        }

        // Step 5: Mark the task as processed to prevent duplicate transactions
        try {
          // Use a transaction for updating task status
          await AppDataSource.transaction(async transactionalEntityManager => {
            await transactionalEntityManager.createQueryBuilder()
              .update(FarmingPlanTask)
              .set({
                stock_entries_processed: true,
                status: TaskStatus.DONE,
                modified: new Date(),
                modified_by: user.user_id
              })
              .where("name = :taskId", { taskId: workflowData.task_id })
              .execute();
          });

          Logger.info('Successfully marked task as processed', { task_id: workflowData.task_id });
          completedOperations.push({ type: 'task_status_update', ids: [workflowData.task_id] });

        } catch (updateError) {
          // Critical error: We've processed all stock entries but failed to mark the task
          // This could lead to duplicate processing attempts
          Logger.error('Critical: Failed to mark task as processed after successful stock entries:', updateError);
          throw new Error('Critical error: Task processed but not marked as completed. Manual verification required.');
        }

        return {
          success: true,
          task_items: taskItemResult,
          production: productionResult,
          stock_entries: stockEntryResults
        };

      } catch (stockError) {
        // Any error during stock transaction processing
        // Provide guidance for manual verification if any operations completed
        let errorMessage = 'Đã có lỗi xảy ra, có thể tồn kho không đủ số lượng, vui lòng kiểm tra lại';

        if (completedOperations.length > 0) {
          errorMessage += 'Some operations may have completed before the error. Please verify inventory and consider stock reconciliation. ';
          errorMessage += `Completed operations: ${JSON.stringify(completedOperations)}`;
        }

        throw new HttpError(500, errorMessage);
      }
    } catch (error) {
      Logger.error('Error executing workflow:', error);

      if (error instanceof HttpError) {
        throw error;
      } else {
        console.log(error);
        throw new HttpError(500, `Xác nhận công việc thất bại`);
      }
    }
  }

  /**
   * Process task items (warehouse_item_task_used)
   * 
   * @param user Current user
   * @param taskItems Array of task items to process
   * @returns Result of processing
   */
  private async processTaskItems(
    user: ICurrentUser,
    taskItems: TaskItemDto[]
  ): Promise<any> {
    try {
      if (!taskItems || taskItems.length === 0) {
        return { success: true, message: 'No task items to process' };
      }

      // Prepare the items for creation/update
      const preparedItems = taskItems.map(item => ({
        name: item.name || uuidv4(),
        task_id: item.task_id,
        iot_category_id: item.iot_category_id,
        quantity: item.quantity,
        loss_quantity: item.loss_quantity || 0,
        issued_quantity: item.issued_quantity || 0,
        description: item.description || '',
        total_qty_in_crop: item.total_qty_in_crop || 0,
        active_uom: item.active_uom || '',
        active_conversion_factor: item.active_conversion_factor || 1,
        creation: moment().format('YYYY-MM-DD HH:mm:ss')
      }));

      // Process existing and new items
      const existingItems = preparedItems.filter(item => item.name && item.name.indexOf('new-') !== 0);
      const newItems = preparedItems.filter(item => !item.name || item.name.indexOf('new-') === 0);

      let result;

      // Create new items
      if (newItems.length > 0) {
        result = await this.taskItemService.createTaskItemAdmin(user, newItems);
      }

      // Update existing items
      if (existingItems.length > 0) {
        result = await this.taskItemService.updateTaskItemAdmin(user, existingItems);
      }

      return result;
    } catch (error) {
      Logger.error('Error processing task items:', error);
      throw error;
    }
  }

  /**
   * Process production items (production_quantity)
   * 
   * @param user Current user
   * @param productionItems Array of production items to process
   * @returns Result of processing
   */
  private async processProductionItems(
    user: ICurrentUser,
    productionItems: ProductionDto[]
  ): Promise<any> {
    try {
      if (!productionItems || productionItems.length === 0) {
        return { success: true, message: 'No production items to process' };
      }

      // Prepare the items for creation/update
      const preparedItems = productionItems.map(item => ({
        name: item.name || uuidv4(),
        task_id: item.task_id,
        product_id: item.product_id,
        quantity: item.quantity,
        exp_quantity: item.exp_quantity || item.quantity,
        lost_quantity: item.lost_quantity || 0,
        issued_quantity: item.issued_quantity || 0,
        finished_quantity: item.finished_quantity || 0, // Add finished_quantity field
        description: item.description || '',
        total_qty_in_crop: item.total_qty_in_crop || 0,
        active_uom: item.active_uom || '',
        active_conversion_factor: item.active_conversion_factor || 1,
        creation: moment().format('YYYY-MM-DD HH:mm:ss')
      }));

      // Process existing and new items
      const existingItems = preparedItems.filter(item => item.name && item.name.indexOf('new-') !== 0);
      const newItems = preparedItems.filter(item => !item.name || item.name.indexOf('new-') === 0);

      // Add oldID property required by IIotProductionQuantity interface
      const newItemsWithOldId = newItems.map(item => ({
        ...item,
        oldID: ''
      }));

      const existingItemsWithOldId = existingItems.map(item => ({
        ...item,
        oldID: ''
      }));

      let result;

      // Create new items
      if (newItemsWithOldId.length > 0) {
        result = await this.taskProductionService.createTaskProductionAdmin(user, newItemsWithOldId);
      }

      // Update existing items
      if (existingItemsWithOldId.length > 0) {
        result = await this.taskProductionService.updateTaskProductionAdmin(user, existingItemsWithOldId);
      }

      return result;
    } catch (error) {
      Logger.error('Error processing production items:', error);
      throw error;
    }
  }

  /**
   * Creates a Stock Entry in ERPNext for inventory tracking
   * 
   * @param user Current user
   * @param params Stock entry parameters
   * @returns Result of stock entry creation
   */
  async createStockEntry(
    user: ICurrentUser,
    params: {
      purpose: string; // 'Material Transfer', 'Material Issue', 'Material Receipt'
      items: Array<{
        item_code: string;
        qty: number;
        uom: string;
        conversion_factor: number;
        s_warehouse?: string;
        t_warehouse?: string;
        basic_rate?: number;
      }>;
      description: string;
      iot_crop: string;
      iot_farming_plan_task: string;
    }
  ): Promise<any> {
    try {
      // Chuẩn bị payload stockEntry theo định dạng mẫu
      const stockEntry: any = {
        __isLocal: 1,
        __unsaved: 1,
        posting_date: moment().format('YYYY-MM-DD'),
        posting_time: moment().format('HH:mm:ss'),
        set_posting_time: 1,
        company: 'VIIS',
        description: params.description,
        iot_customer_user: user.user_id,
        items: params.items.map(item => {
          // Tạo 6 ký tự ngẫu nhiên cho ID mỗi item như trong mẫu (ví dụ: t4cxpz)
          const randomChars = 'abcdefghijklmnopqrstuvwxyz0123456789';
          let randomId = '';
          for (let i = 0; i < 6; i++) {
            randomId += randomChars.charAt(Math.floor(Math.random() * randomChars.length));
          }

          const stockItem: any = {
            conversion_factor: item.conversion_factor,
            uom: item.uom,
            item_code: item.item_code,
            qty: item.qty,
            doctype: 'Stock Entry Detail',
            name: `new-stock-entry-detail-${randomId}`
          };

          // Thêm s_warehouse nếu có
          if (item.s_warehouse) {
            stockItem.s_warehouse = item.s_warehouse;
          }

          // Thêm t_warehouse nếu có
          if (item.t_warehouse) {
            stockItem.t_warehouse = item.t_warehouse;
          }

          // Đối với Material Receipt, thêm basic_rate và basic_amount
          if (params.purpose === 'Material Receipt' && item.basic_rate) {
            stockItem.__islocal = 1;
            stockItem.__unsaved = 1;
            stockItem.basic_rate = item.basic_rate;
            stockItem.basic_amount = item.basic_rate * item.qty;
            stockItem.transfer_qty = item.qty;
          }

          return stockItem;
        }),
        doctype: 'Stock Entry',
        purpose: params.purpose,
        stock_entry_type: params.purpose,
        iot_crop: params.iot_crop,
        iot_farming_plan_task: params.iot_farming_plan_task
      };

      // Create and submit the stock entry using the appropriate methods from StockEntryService
      // First save the stock entry
      const result = await this.stockEntryService.saveStockEntryAdmin(user, stockEntry);
      // Then submit it
      const submitResult = await this.stockEntryService.submitStockEntryAdmin(user, result.docs[0], { doc: result.docs[0].name, action: 'Submit' });

      return {
        success: true,
        stockEntry: result,
        submitResult
      };
    } catch (error) {
      Logger.error('Error creating stock entry:', error);
      throw error;
    }
  }

  /**
   * Validates that the task exists and the user has access to it
   * 
   * @param user Current user
   * @param taskId Task ID to validate
   * @returns Task object if valid, null otherwise
   */
  async validateTaskAccess(
    user: ICurrentUser,
    taskId: string
  ): Promise<{ name: string; label: string } | null> {
    try {
      const task = await ERPExecute(
        `
            SELECT task.name, task.label
            FROM tabiot_farming_plan_task AS task
            LEFT JOIN tabiot_farming_plan_state AS state ON task.farming_plan_state = state.name
            LEFT JOIN tabiot_farming_plan AS plan ON state.farming_plan = plan.name
            LEFT JOIN tabiot_crop AS crop ON plan.crop = crop.name
            LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name
            LEFT JOIN tabiot_customer AS customer ON zone.customer_id = customer.name

            WHERE task.name = $1 AND customer.name = $2
          `,
        [taskId, user.customer_id.toString() || '']
      );
      return task?.length > 0 ? task[0] : null;
    } catch (error) {
      Logger.error('Error validating task access:', error);
      return null;
    }
  }

  /**
   * Gets crop information for a specific task
   * 
   * @param user Current user
   * @param taskId Task ID
   * @returns Crop information including ID and name
   */


  /**
   * Process task items with origin tracking
   * Updates WarehouseItemTaskUsed entities and creates TaskItemTransfer records for tracing
   * 
   * @param user Current user
   * @param executeData Work execution data
   * @returns Array of updated items and created transfers
   * @private
   */
  private async processTaskItemsWithOrigin(
    user: ICurrentUser,
    executeData: ExecuteWorkDto
  ): Promise<any[]> {
    const results = [];
    Logger.debug('Starting processTaskItemsWithOrigin', {
      current_task_id: executeData.current_task_id,
      itemCount: executeData.task_items?.length || 0
    });

    // Process each task item
    for (const itemData of executeData.task_items) {
      try {
        Logger.debug('Processing task item', {
          item_id: itemData.item_id,
          current_task_id: executeData.current_task_id
        });

        // need to find task origin from other task's production quantity with same product_id like item_id
        const originTask = await this.findOriginTask(user, executeData.current_task_id, itemData.item_id);
        itemData.origin_task_id = originTask?.task_id;

        // Find and update warehouse item for current task
        const warehouseItemRepo = AppDataSource.getRepository(WarehouseItemTaskUsed);

        // Check if item already exists
        let taskItem = await warehouseItemRepo.findOne({
          where: {
            task_id: executeData.current_task_id,
            iot_category_id: itemData.item_id
          }
        });

        // Create new item if not exists
        if (!taskItem) {
          Logger.debug('Creating new WarehouseItemTaskUsed', {
            task_id: executeData.current_task_id,
            iot_category_id: itemData.item_id
          });

          taskItem = new WarehouseItemTaskUsed();
          taskItem.name = uuidv4();
          taskItem.task_id = executeData.current_task_id;
          taskItem.iot_category_id = itemData.item_id;
          taskItem.quantity = 0;
          taskItem.draft_quantity = 0;
          taskItem.description = itemData.description || '';
          taskItem.active_uom = itemData.active_uom || undefined;
          taskItem.active_conversion_factor = itemData.active_conversion_factor || 1;

          // Log the entity before saving
          Logger.debug('New WarehouseItemTaskUsed entity', { taskItem });
        }

        // Update existing or new item
        taskItem.draft_quantity = itemData.draft_quantity;

        // Save the task item
        Logger.debug('Saving WarehouseItemTaskUsed', { taskItem });
        let savedItem;
        try {
          savedItem = await warehouseItemRepo.save(taskItem);
          Logger.debug('Successfully saved WarehouseItemTaskUsed', { savedItem });
        } catch (error: unknown) {
          const saveError = error as Error & {
            driverError?: {
              code?: string;
              detail?: string;
              constraint?: string;
              table?: string;
            };
          };

          Logger.error('Error saving WarehouseItemTaskUsed', {
            error: saveError.message,
            taskItem,
            errorDetails: {
              message: saveError.message,
              stack: saveError.stack,
              ...(saveError.driverError && {
                driverError: {
                  code: saveError.driverError?.code,
                  detail: saveError.driverError?.detail,
                  constraint: saveError.driverError?.constraint,
                  table: saveError.driverError?.table
                }
              })
            }
          });
          throw saveError;
        }

        let transfer = null;

        // Only create transfer if origin_task_id is defined
        if (itemData.origin_task_id) {
          // Validate origin task and origin task access
          const originTask = await this.validateTaskAccess(user, itemData.origin_task_id);
          if (!originTask) {
            throw new HttpError(404, `Origin task ${itemData.origin_task_id} not found or access denied`);
          }
          transfer = await this.taskItemTransferService.createItemTransfer(
            user,
            itemData.origin_task_id,
            executeData.current_task_id,
            itemData.item_id,
            itemData.draft_quantity,
            itemData.active_uom,
            itemData.active_conversion_factor || 1,
            `Transferred during work execution on ${new Date().toISOString()}`
          );
        }

        results.push({
          item: savedItem,
          transfer: transfer
        });
      } catch (error) {
        Logger.error(`Error processing task item ${itemData.item_id}:`, error);
        throw error;
      }
    }

    return results;
  }

  /**
   * Process production items for work execution
   * Updates existing production items or creates new ones if needed
   * Only updates draft quantities without affecting actual quantities
   * 
   * @param user Current user
   * @param executeData Work execution data
   * @returns Array of updated or created production items
   * @private
   */
  private async processProductionItemsForWork(
    user: ICurrentUser,
    executeData: ExecuteWorkDto
  ): Promise<ProductionQuantity[]> {
    const results = [];

    // Process each production item
    for (const prodData of executeData.task_production) {
      try {
        // Find and update production quantity for current task
        const prodRepo = AppDataSource.getRepository(ProductionQuantity);

        // Check if production already exists
        let prodItem = await prodRepo.findOne({
          where: {
            task_id: executeData.current_task_id,
            product_id: prodData.item_id
          }
        });

        // Create new production if not exists
        if (!prodItem) {
          prodItem = new ProductionQuantity();
          prodItem.name = uuidv4();
          prodItem.task_id = executeData.current_task_id;
          prodItem.product_id = prodData.item_id;
          prodItem.quantity = 0;
          prodItem.draft_quantity = 0;
          prodItem.description = prodData.description || '';
          prodItem.active_uom = prodData.active_uom || undefined;
          prodItem.active_conversion_factor = prodData.active_conversion_factor || 1;
        }

        // Update existing or new production item 
        // Only add to draft quantity, not actual quantity (which will be updated by supervisor approval)
        prodItem.draft_quantity = prodData.draft_quantity;

        // Save the production item
        const savedProd = await prodRepo.save(prodItem);
        results.push(savedProd);
      } catch (error) {
        Logger.error(`Error processing production item ${prodData.item_id}:`, error);
        throw error;
      }
    }

    return results;
  }

  /**
   * Executes work on a task, updating the task, updating items and production quantities,
   * and creating item transfers for tracing between tasks
   * 
   * @param user Current user
   * @param executeData Work execution data with task items and production quantities
   * @returns Result of the work execution including updated items and transfers
   */
  async executeWork(
    user: ICurrentUser,
    executeData: ExecuteWorkDto
  ): Promise<any> {
    try {
      // Validate task exists and user has access
      const currentTask = await this.validateTaskAccess(user, executeData.current_task_id);
      if (!currentTask) {
        throw new HttpError(404, `Task ${executeData.current_task_id} not found or access denied`);
      }

      // Get task details from database
      const taskRepo = AppDataSource.getRepository(FarmingPlanTask);
      const taskEntity = await taskRepo.findOne({
        where: { name: executeData.current_task_id },
      });

      if (!taskEntity) {
        throw new HttpError(404, `Task ${executeData.current_task_id} not found in database`);
      }

      // Update task fields from DTO
      //fixed status to In progress
      taskEntity.status = TaskStatus.IN_PROGRESS;

      if (executeData.description !== undefined) {
        taskEntity.description = executeData.description;
      }

      if (executeData.start_date) {
        taskEntity.start_date = new Date(executeData.start_date);
      }

      if (executeData.end_date) {
        taskEntity.end_date = new Date(executeData.end_date);
      }

      if (executeData.image) {
        taskEntity.image = executeData.image;
      }

      // Save updated task
      const updatedTask = await taskRepo.save(taskEntity);
      Logger.debug('Updated task', {
        taskId: updatedTask.name, updates: {
          status: 'In progress',
          start_date: executeData.start_date,
          end_date: executeData.end_date
        }
      });

      // Process task items and create transfers
      const createdItems = await this.processTaskItemsWithOrigin(user, executeData);

      // Process production items
      const createdProduction = await this.processProductionItemsForWork(user, executeData);
      await this.labelPrintManagementService.autoCreateLabelRequests(executeData.current_task_id, user.user_id, user.customer_id);
      return {
        task: taskEntity,
        items: createdItems,
        production: createdProduction
      };
    } catch (error) {
      Logger.error('Error executing work:', error);
      throw error;
    }
  }

  /**
   * Finds the origin task that produced the specified item
   * Searches through production quantities to find tasks that have produced the item
   *
   * @param user Current user
   * @param currentTaskId Current task ID to exclude from search
   * @param itemId Item ID to find the origin task for
   * @returns Origin task information or null if not found
   */
  async findOriginTask(
    user: ICurrentUser,
    currentTaskId: string,
    itemId: string
  ): Promise<{ task_id: string; task_label?: string; quantity?: number } | null> {
    try {
      Logger.debug('Finding origin task', {
        currentTaskId,
        itemId,
        userId: user.user_id
      });

      // Validate input parameters
      if (!currentTaskId || !itemId) {
        Logger.warn('Invalid parameters for findOriginTask', { currentTaskId, itemId });
        return null;
      }

      // Query production quantities to find tasks that produced this item
      // Exclude the current task and look for tasks in the same crop
      const originTaskQuery = await ERPExecute(
        `
          SELECT
            pq.task_id,
            task.label AS task_label,
            pq.quantity,
            pq.finished_quantity,
            task.status,
            task.end_date
          FROM tabiot_production_quantity AS pq
          JOIN tabiot_farming_plan_task AS task ON pq.task_id = task.name
          JOIN tabiot_farming_plan_state AS state ON task.farming_plan_state = state.name
          JOIN tabiot_farming_plan AS plan ON state.farming_plan = plan.name
          JOIN tabiot_crop AS crop ON plan.crop = crop.name
          JOIN tabiot_zone AS zone ON crop.zone_id = zone.name
          -- Join current task to get its crop for comparison
          JOIN tabiot_farming_plan_task AS current_task ON current_task.name = $2
          JOIN tabiot_farming_plan_state AS current_state ON current_task.farming_plan_state = current_state.name
          JOIN tabiot_farming_plan AS current_plan ON current_state.farming_plan = current_plan.name
          JOIN tabiot_crop AS current_crop ON current_plan.crop = current_crop.name
          WHERE pq.product_id = $1
            AND pq.task_id != $2
            AND pq.deleted IS NULL
            AND task.deleted IS NULL
            AND (pq.quantity > 0 OR pq.finished_quantity > 0)
            AND zone.customer_id = $3
            -- Ensure both tasks are in the same crop
            AND crop.name = current_crop.name
          ORDER BY task.end_date DESC, pq.finished_quantity DESC, pq.quantity DESC
          LIMIT 1
        `,
        [itemId, currentTaskId, user.customer_id?.toString()]
      );

      if (originTaskQuery?.length > 0) {
        const originTask = originTaskQuery[0] as any;
        Logger.debug('Found origin task', {
          originTaskId: originTask.task_id,
          originTaskLabel: originTask.task_label,
          quantity: originTask.quantity,
          finishedQuantity: originTask.finished_quantity
        });

        return {
          task_id: originTask.task_id,
          task_label: originTask.task_label,
          quantity: originTask.finished_quantity || originTask.quantity
        };
      }

      Logger.debug('No origin task found', { currentTaskId, itemId });
      return null;
    } catch (error) {
      Logger.error('Error finding origin task:', error);
      return null;
    }
  }

  async getCropInfoForTask(
    user: ICurrentUser,
    taskId: string
  ): Promise<{ crop_id: string; crop_name: string } | null> {
    try {
      const cropInfo = await ERPExecute(
        `
            SELECT
              crop.name AS crop_id,
              crop.label AS crop_name
            FROM tabiot_farming_plan_task AS task
            LEFT JOIN tabiot_farming_plan_state AS state ON task.farming_plan_state = state.name
            LEFT JOIN tabiot_farming_plan AS plan ON state.farming_plan = plan.name
            LEFT JOIN tabiot_crop AS crop ON plan.crop = crop.name
            WHERE task.name = $1
          `,
        [taskId]
      );

      // Type checking for crop info
      if (cropInfo?.length > 0) {
        const result = cropInfo[0] as any;
        return {
          crop_id: result.crop_id,
          crop_name: result.crop_name
        };
      }

      return null;
    } catch (error) {
      Logger.error('Error getting crop info for task:', error);
      return null;
    }
  }
}