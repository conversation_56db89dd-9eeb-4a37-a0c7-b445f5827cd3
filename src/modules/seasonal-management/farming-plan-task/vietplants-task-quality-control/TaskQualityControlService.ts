import Container, { Service } from 'typedi';
import { HttpError } from 'routing-controllers';
import { ICurrentUser } from '@app/interfaces';
import { v4 as uuidv4 } from 'uuid';
import moment from 'moment';
import { Logger } from '@app/loaders/logger';
import { applyQueryFilters, FilterTuple } from '@app/utils/helpers/queryHelpter';
import { TaskItemService } from '@app/modules/farming-plan/task/task-item/TaskItemService';
import { TaskProductionService } from '@app/modules/farming-plan/task/task-production/TaskProductionService';
import { StockEntryService } from '@app/modules/stock-v3/stock-entry/StockEntryService';
import { CropService } from '@app/modules/crop-manage/crop/CropService';
import { ERPExecute, ERPExecuteWithTransaction } from '@app/loaders/pglib/PGDB';
import { AppDataSource } from '@app/orm/dataSource';
import { FarmingPlanTask, TaskStatus } from '@app/orm/entities/farmingPlan/FarmingPlanTask';
import { ProductionQuantity } from '@app/orm/entities/farmingPlan/taskItem/ProductionQuantity';
import { FarmingPlanTaskQualityControl, InfectedType } from '@app/orm/entities/farmingPlan/taskItem/FarmingPlanTaskQualityControl';
import { ApproveQualityControlDto, QualityControlItemDto, StockEntryItemDto, SubmitQualityControlDto, UpdateProductionDto, BatchSubmitQualityControlDto, QualityCheckTaskDto, BatchApproveQualityControlDto, BatchApprovalResponseDto, BatchApprovalResultItemDto } from './TaskQualityControl.dto';
import { IotCustomerUser } from '@app/orm/entities/customer/customer_user';

// Constants for quality control workflow
const DEFAULT_COMPANY = 'VIIS';
const DEFAULT_CONVERSION_FACTOR = 1;
const STOCK_ENTRY_PURPOSE_MATERIAL_ISSUE = 'Material Issue';

// Quality control status constants
const QC_STATUS_DRAFT = 'Draft';
const QC_STATUS_APPROVED = 'Approved';
const TASK_STATUS_DONE = 'Done';

/**
 * Service for managing the task quality control workflow
 * This includes quality inspection and production quality tracking for tasks
 */
@Service()
export class TaskQualityControlService {
  constructor(
    private stockEntryService: StockEntryService,
  ) {
    this.stockEntryService = Container.get(StockEntryService)
  }
  private taskRepo = AppDataSource.getRepository(FarmingPlanTask);
  /**
   * Validates that the task exists and the user has access to it
   * 
   * @param user Current user
   * @param taskId Task ID to validate
   * @returns Task object if valid, null otherwise
   */
  async validateTaskAccess(
    user: ICurrentUser,
    taskId: string
  ): Promise<{ name: string; label: string; status: any } | null> {
    try {
      const task = await ERPExecute(`
            SELECT task.name, task.label, task.status
            FROM tabiot_farming_plan_task AS task
            LEFT JOIN tabiot_farming_plan_state AS state ON task.farming_plan_state = state.name
            LEFT JOIN tabiot_farming_plan AS plan ON state.farming_plan = plan.name
            LEFT JOIN tabiot_crop AS crop ON plan.crop = crop.name
            LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name
            LEFT JOIN tabiot_customer AS customer ON zone.customer_id = customer.name

            WHERE task.name = $1 AND customer.name = $2
          `,
        [taskId, user.customer_id.toString() || '']
      );

      return task?.length > 0 ? task[0] : null;
    } catch (error) {
      Logger.error('Error validating task access:', error);
      return null;
    }
  }

  /**
   * Gets crop information for a task
   * Includes crop ID and name for reference in stock entries
   * 
   * @param user Current user
   * @param taskId Task ID
   * @returns Crop info object or null
   */
  async getCropInfoForTask(
    user: ICurrentUser,
    taskId: string
  ): Promise<{ crop_id: string; crop_name: string } | null> {
    try {
      const cropInfo = await ERPExecute(
        `
            SELECT 
              crop.name AS crop_id,
              crop.label AS crop_name
            FROM tabiot_farming_plan_task AS task
            LEFT JOIN tabiot_farming_plan_state AS state ON task.farming_plan_state = state.name
            LEFT JOIN tabiot_farming_plan AS plan ON state.farming_plan = plan.name
            LEFT JOIN tabiot_crop AS crop ON plan.crop = crop.name
            WHERE task.name = $1
          `,
        [taskId]
      );

      // Type checking for crop info
      if (cropInfo?.length > 0) {
        const result = cropInfo[0] as any;
        return {
          crop_id: result.crop_id,
          crop_name: result.crop_name
        };
      }

      return null;
    } catch (error) {
      Logger.error('Error getting crop info for task:', error);
      return null;
    }
  }

  /**
   * Creates a Stock Entry in ERPNext for inventory tracking
   * 
   * @param user Current user
   * @param params Stock entry parameters
   * @returns Result of stock entry creation
   */
  async createStockEntry(
    user: ICurrentUser,
    params: {
      purpose: string;
      items: Array<{
        item_code: string;
        qty: number;
        uom: string;
        conversion_factor: number;
        s_warehouse?: string;
        t_warehouse?: string;
        basic_rate?: number;
      }>;
      description: string;
      iot_crop: string;
      iot_farming_plan_task: string;
    }
  ): Promise<any> {
    try {
      // Validate required fields with specific error messages
      if (!params.purpose) {
        throw new HttpError(400, 'Purpose is required for stock entry');
      }
      if (!params.items) {
        throw new HttpError(400, 'Items array is required for stock entry');
      }
      if (params.items.length === 0) {
        throw new HttpError(400, 'At least one item is required for stock entry');
      }

      // Create stock entry items
      const stockEntryItems: StockEntryItemDto[] = params.items.map(item => {
        return {
          name: `row ${uuidv4()}`,
          item_code: item.item_code,
          qty: item.qty,
          uom: item.uom,
          conversion_factor: item.conversion_factor,
          doctype: 'Stock Entry Detail',
          s_warehouse: item.s_warehouse,
          t_warehouse: item.t_warehouse
        };
      });

      // Create stock entry
      const stockEntry = {
        __islocal: 1,
        __unsaved: 1,
        posting_date: moment().format('YYYY-MM-DD'),
        posting_time: moment().format('HH:mm:ss'),
        set_posting_time: 1,
        doctype: 'Stock Entry',
        company: DEFAULT_COMPANY,
        purpose: params.purpose,
        stock_entry_type: params.purpose,
        iot_customer_user: user.user_id.toString(),
        iot_crop: params.iot_crop,
        iot_farming_plan_task: params.iot_farming_plan_task,
        description: params.description,
        items: stockEntryItems
      };

      // Submit the stock entry to ERPNext
      const result = await this.stockEntryService.saveStockEntryAdmin(user, stockEntry);

      // Submit the created stock entry
      const submitResult = await this.stockEntryService.submitStockEntryAdmin(user, result.docs[0], { doc: result.docs[0].name, action: 'Submit' });

      return submitResult;
    } catch (error) {
      Logger.error('Error creating stock entry:', error);
      throw error;
    }
  }

  /**
   * Validates quality control item quantities
   * @param item Quality control item to validate
   * @throws HttpError if validation fails
   */
  private validateQualityControlQuantities(item: QualityControlItemDto): void {
    // Validate non-negative quantities
    if (item.total_items < 0) {
      throw new HttpError(400, 'Total items must be non-negative');
    }
    if (item.infected_items < 0) {
      throw new HttpError(400, 'Infected items must be non-negative');
    }
    if (item.quality_passed_items !== undefined && item.quality_passed_items < 0) {
      throw new HttpError(400, 'Quality passed items must be non-negative');
    }

    // Validate that infected_items doesn't exceed total_items
    if (item.infected_items > item.total_items) {
      throw new HttpError(400, 'Infected items cannot exceed total items');
    }

    // If quality_passed_items is provided, validate the sum
    if (item.quality_passed_items !== undefined) {
      const sum = item.infected_items + item.quality_passed_items;
      if (sum > item.total_items) {
        throw new HttpError(400, 'Sum of infected items and quality passed items cannot exceed total items');
      }
    }
  }

  /**
   * Submits a quality control check for a task
   * Records the inspection results in the farming_plan_task_quality_control table
   *
   * @param user Current user
   * @param qualityData Quality control data
   * @returns Created quality control record
   */
  async submitQualityControl(
    user: ICurrentUser,
    qualityData: SubmitQualityControlDto
  ): Promise<FarmingPlanTaskQualityControl> {
    try {
      // Validate task exists and user has access
      const task = await this.validateTaskAccess(user, qualityData.task_id);
      if (!task) {
        throw new HttpError(404, `Task ${qualityData.task_id} not found or access denied`);
      }

      // Validate quality control quantities for each item
      for (const item of qualityData.quality_items) {
        this.validateQualityControlQuantities(item);
      }

      // Get first production quantity for this task to use for item_id and uom_id
      const productionQuantity = await this.getFirstProductionQuantity(qualityData.task_id);
      if (!productionQuantity) {
        throw new HttpError(404, `No production quantity found for task ${qualityData.task_id}`);
      }

      // Get repository
      const qualityControlRepo = AppDataSource.getRepository(FarmingPlanTaskQualityControl);

      // Find all existing quality control records for this task
      const allExistingControls = await qualityControlRepo.find({
        where: {
          task_id: qualityData.task_id
        }
      });

      // Filter out quality items that already have a matching record
      const itemsToCreate = qualityData.quality_items.filter(qualityItem => {
        // Check if there's an existing record with the same task_id AND infected_type
        const matchingRecord = allExistingControls.find(control =>
          control.task_id === qualityData.task_id &&
          control.infected_type === qualityItem.infected_type
        );

        // If no matching record was found, we should create this item
        return !matchingRecord;
      });

      Logger.info('Quality control filtering', {
        task_id: qualityData.task_id,
        totalItems: qualityData.quality_items.length,
        existingItems: allExistingControls.length,
        itemsToCreate: itemsToCreate.length
      });

      // If all items already exist, return the first existing record
      if (itemsToCreate.length === 0 && allExistingControls.length > 0) {
        Logger.info('All quality control items already exist for this task', {
          task_id: qualityData.task_id
        });

        // Return the first existing record
        return allExistingControls[0];
      }

      // Process each quality control item
      for (const item of itemsToCreate) {
        // Create quality control record
        const qualityControl = new FarmingPlanTaskQualityControl();
        qualityControl.name = uuidv4();
        qualityControl.task_id = qualityData.task_id;
        qualityControl.item_id = productionQuantity.product_id;
        qualityControl.uom_id = productionQuantity.active_uom;
        qualityControl.total_items = item.total_items;
        qualityControl.infected_items = item.infected_items;
        qualityControl.quality_passed_items = item.quality_passed_items || 0;
        qualityControl.infected_type = item.infected_type;
        qualityControl.inspection_date = qualityData.inspection_date ? new Date(qualityData.inspection_date) : new Date();
        qualityControl.inspected_by = user.user_id.toString();
        qualityControl.description = qualityData.description || '';
        qualityControl.status = QC_STATUS_DRAFT;

        // Set customer ID from user
        if (user.customer_id) {
          qualityControl.customer_id = user.customer_id.toString();
        }

        // Save quality control record
        await qualityControlRepo.save(qualityControl);
      }

      // Get all quality control records for the task (both existing and newly created)
      const qualityControls = await qualityControlRepo.find({
        where: {
          task_id: qualityData.task_id
        },
        relations: ['item']
      });

      Logger.info('Completed submitQualityControl', {
        task_id: qualityData.task_id,
        totalRecords: qualityControls.length,
        itemsCreated: itemsToCreate.length,
        itemsSkipped: qualityData.quality_items.length - itemsToCreate.length
      });

      // Return the first record
      return qualityControls[0];
    } catch (error) {
      Logger.error('Error submitting quality control:', error);
      throw error;
    }
  }

  /**
   * Gets the first production quantity for a task
   * Used to retrieve item_id and uom_id when not provided in quality control items
   * 
   * @param taskId Task ID to get production quantity for
   * @returns First production quantity for the task or null if none exists
   */
  async getFirstProductionQuantity(taskId: string): Promise<ProductionQuantity | null> {
    try {
      const productionRepo = AppDataSource.getRepository(ProductionQuantity);
      const productionQuantity = await productionRepo.findOne({
        where: { task_id: taskId },
        relations: ['product']
      });
      return productionQuantity;
    } catch (error) {
      Logger.error('Error getting production quantity:', error);
      return null;
    }
  }

  /**
   * Process a single quality check task
   * Creates quality control records for each quality item in the task
   * 
   * @param user Current user
   * @param qualityTask Quality check task data
   * @returns Created quality control records
   */
  async processQualityCheckTask(
    user: ICurrentUser,
    qualityTask: QualityCheckTaskDto
  ): Promise<FarmingPlanTaskQualityControl[]> {
    try {
      Logger.info('Starting processQualityCheckTask', {
        userId: user.user_id,
        taskId: qualityTask.task_id,
        qualityItemsCount: qualityTask.quality_items?.length || 0
      });

      // Validate task exists and user has access
      const task = await this.validateTaskAccess(user, qualityTask.task_id);
      if (!task) {
        Logger.error('Task validation failed', { taskId: qualityTask.task_id, userId: user.user_id });
        throw new HttpError(404, `Task ${qualityTask.task_id} not found or access denied`);
      }

      Logger.info('Task validation passed', { taskId: qualityTask.task_id, taskLabel: task.label });

      // Get first production quantity for this task to use for item_id and uom_id if not provided
      const productionQuantity = await this.getFirstProductionQuantity(qualityTask.task_id);
      if (!productionQuantity) {
        Logger.error('No production quantity found', { taskId: qualityTask.task_id });
        throw new HttpError(404, `No production quantity found for task ${qualityTask.task_id}`);
      }

      Logger.info('Production quantity found', {
        taskId: qualityTask.task_id,
        productId: productionQuantity.product_id,
        uomId: productionQuantity.active_uom
      });

      // Get repository
      const qualityControlRepo = AppDataSource.getRepository(FarmingPlanTaskQualityControl);

      // Find all existing quality control records for this task
      const allExistingControls = await qualityControlRepo.find({
        where: { task_id: qualityTask.task_id }
      });
      console.log("allExistingControls", allExistingControls);

      Logger.info('Existing quality controls found', {
        taskId: qualityTask.task_id,
        existingCount: allExistingControls.length,
        existingTypes: allExistingControls.map(c => c.infected_type)
      });

      // Track created quality controls
      const createdQualityControls: FarmingPlanTaskQualityControl[] = [];

      // Process each quality control item
      for (const item of qualityTask.quality_items) {
        // Validate quality control quantities
        this.validateQualityControlQuantities(item);

        // Check if there's an existing record with the same task_id AND infected_type
        const matchingRecord = allExistingControls.find(control =>
          control.task_id === qualityTask.task_id &&
          control.infected_type === item.infected_type
        );

        // Skip if a record with this task_id and infected_type already exists
        if (matchingRecord) {
          continue;
        }

        // Create quality control record
        const qualityControl = new FarmingPlanTaskQualityControl();
        qualityControl.name = uuidv4();
        qualityControl.task_id = qualityTask.task_id;

        // Use item_id from request or from production quantity
        qualityControl.item_id = productionQuantity.product_id;

        // Use uom_id from request or from production quantity
        qualityControl.uom_id = productionQuantity.active_uom;

        qualityControl.total_items = item.total_items;
        qualityControl.infected_items = item.infected_items;
        qualityControl.quality_passed_items = item.quality_passed_items || 0;
        qualityControl.infected_type = item.infected_type;
        qualityControl.inspection_date = qualityTask.inspection_date ? new Date(qualityTask.inspection_date) : new Date();
        qualityControl.inspected_by = user.user_id.toString();
        qualityControl.description = qualityTask.description || '';
        qualityControl.status = QC_STATUS_DRAFT;

        // Set customer ID from user
        if (user.customer_id) {
          qualityControl.customer_id = user.customer_id.toString();
        }

        // Save quality control record
        await qualityControlRepo.save(qualityControl);
        createdQualityControls.push(qualityControl);
      }

      return createdQualityControls;
    } catch (error) {
      Logger.error('Error processing quality check task:', error);
      throw error;
    }
  }

  /**
   * Updates a task's status to In Progress
   * 
   * @param user Current user
   * @param taskId Task ID to update
   * @returns Updated task
   */
  async updateTaskStatusToInProgress(
    user: ICurrentUser,
    taskId: string
  ): Promise<FarmingPlanTask> {
    try {
      // Validate task exists and user has access
      const task = await this.validateTaskAccess(user, taskId);
      if (!task) {
        throw new HttpError(404, `Task ${taskId} not found or access denied`);
      }

      // Update task status to In Progress
      task.status = 'In progress'; // Using correct case as defined in FarmingPlanTask entity
      await this.taskRepo.save(task);

      return task as FarmingPlanTask;
    } catch (error) {
      Logger.error('Error updating task status:', error);
      throw error;
    }
  }

  /**
   * Submits batch quality control checks for multiple tasks
   * Updates current task status to In Progress and processes each quality check task
   * 
   * @param user Current user
   * @param batchData Batch quality control data
   * @returns Results of batch quality control submission
   */
  async submitBatchQualityControl(
    user: ICurrentUser,
    batchData: BatchSubmitQualityControlDto
  ): Promise<{
    currentTask: FarmingPlanTask;
    qualityControls: FarmingPlanTaskQualityControl[][];
  }> {
    try {
      Logger.info('Starting batch quality control submission', {
        current_task_id: batchData.current_task_id,
        quality_check_tasks_count: batchData.quality_check_tasks.length
      });

      // Update current task status to In Progress
      const currentTask = await this.updateTaskStatusToInProgress(user, batchData.current_task_id);

      // Track all created quality controls
      const allQualityControls: FarmingPlanTaskQualityControl[][] = [];

      // Process each quality check task
      for (const qualityTask of batchData.quality_check_tasks) {
        const qualityControls = await this.processQualityCheckTask(user, qualityTask);
        allQualityControls.push(qualityControls);
      }

      Logger.info('Completed batch quality control submission', {
        current_task_id: batchData.current_task_id,
        tasks_processed: batchData.quality_check_tasks.length,
        controls_created: allQualityControls.flat().length
      });

      return {
        currentTask,
        qualityControls: allQualityControls
      };
    } catch (error) {
      Logger.error('Error submitting batch quality control:', error);
      throw error;
    }
  }

  /**
   * Gets a quality control record by ID
   * 
   * @param user Current user
   * @param qualityControlId Quality control ID
   * @returns Quality control record
   */
  async getQualityControl(
    user: ICurrentUser,
    qualityControlId: string
  ): Promise<FarmingPlanTaskQualityControl> {
    try {
      // Get repository
      const qualityControlRepo = AppDataSource.getRepository(FarmingPlanTaskQualityControl);

      // Find quality control record
      const qualityControl = await qualityControlRepo.findOne({
        where: {
          name: qualityControlId
        },
        relations: ['item', 'uom', 'task', 'inspectedByUser', 'approveByUser', 'task.assignedUser']
      });

      // Validate quality control exists and user has access
      if (!qualityControl || (user.customer?.id && qualityControl.customer_id !== user.customer.id.toString())) {
        throw new HttpError(404, `Quality control ${qualityControlId} not found or access denied`);
      }

      return qualityControl;
    } catch (error) {
      Logger.error('Error getting quality control:', error);
      throw error;
    }
  }

  /**
   * Lists quality control records for a task
   * 
   * @param user Current user
   * @param taskId Task ID
   * @returns List of quality control records
   */
  async listQualityControlsByTask(
    user: ICurrentUser,
    taskId: string
  ): Promise<FarmingPlanTaskQualityControl[]> {
    try {
      // Validate task exists and user has access
      const task = await this.validateTaskAccess(user, taskId);
      if (!task) {
        throw new HttpError(404, `Task ${taskId} not found or access denied`);
      }

      // Get repository
      const qualityControlRepo = AppDataSource.getRepository(FarmingPlanTaskQualityControl);

      // Find quality control records
      const qualityControls = await qualityControlRepo.find({
        where: {
          task_id: taskId,
          customer_id: user.customer?.id?.toString()
        },
        relations: ['item', 'uom']
      });

      return qualityControls;
    } catch (error) {
      Logger.error('Error listing quality controls:', error);
      throw error;
    }
  }

  /**
   * Approves a quality control record
   * Creates stock entries for rejected items and updates production quantities
   * Updates finished quantities and total_qty_in_crop for all related tasks
   * 
   * @param user Current user
   * @param approveData Approval data
   * @returns Updated quality control record
   */
  async approveQualityControl(
    user: ICurrentUser,
    approveData: ApproveQualityControlDto
  ): Promise<FarmingPlanTaskQualityControl> {
    try {
      // Get quality control record
      const qualityControl = await this.getQualityControl(user, approveData.quality_control_id);
      if (!qualityControl) {
        throw new HttpError(404, `Quality control ${approveData.quality_control_id} not found or access denied`);
      }

      // Validate quality control is in Draft status
      if (qualityControl.status !== QC_STATUS_DRAFT) {
        Logger.info('Quality control is already approved, cannot edit or approve again', {
          quality_control_id: approveData.quality_control_id,
          status: qualityControl.status
        });

        // Throw an error to prevent further processing
        throw new HttpError(400, `Cannot approve quality control ${approveData.quality_control_id} because it's already in ${qualityControl.status} status`);
      }

      // Get repositories
      const qualityControlRepo = AppDataSource.getRepository(FarmingPlanTaskQualityControl);
      const productionQuantityRepo = AppDataSource.getRepository(ProductionQuantity);

      // Update quantity fields if provided in approveData
      if (approveData.total_items !== undefined) {
        qualityControl.total_items = approveData.total_items;
      }

      if (approveData.infected_items !== undefined) {
        qualityControl.infected_items = approveData.infected_items;
      }

      if (approveData.quality_passed_items !== undefined) {
        qualityControl.quality_passed_items = approveData.quality_passed_items;
      }

      // Update infected_type if provided
      if (approveData.infected_type !== undefined) {
        qualityControl.infected_type = approveData.infected_type;
      }

      // Validate updated quantities
      if (qualityControl.total_items < 0 || qualityControl.infected_items < 0 || qualityControl.quality_passed_items < 0) {
        throw new HttpError(400, 'All quantities must be non-negative');
      }

      if (qualityControl.infected_items > qualityControl.total_items) {
        throw new HttpError(400, 'Infected items cannot exceed total items');
      }

      const sum = qualityControl.infected_items + qualityControl.quality_passed_items;
      if (sum > qualityControl.total_items) {
        throw new HttpError(400, 'Sum of infected items and quality passed items cannot exceed total items');
      }

      // Record the approver
      qualityControl.approve_by = user.user_id.toString();

      // Get crop info for stock entry
      const cropInfo = await this.getCropInfoForTask(user, qualityControl.task_id);
      if (!cropInfo) {
        throw new HttpError(404, `Could not determine crop for task ${qualityControl.task_id}`);
      }

      // Start transaction
      const queryRunner = AppDataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Find the existing production quantity - don't create a new one
        const productionQuantity = await productionQuantityRepo.findOne({
          where: {
            task_id: qualityControl.task_id,
            product_id: qualityControl.item_id || ''
          }
        });

        // If production quantity doesn't exist, rollback and throw error
        if (!productionQuantity) {
          Logger.error('Production quantity not found for task and product', {
            task_id: qualityControl.task_id,
            product_id: qualityControl.item_id
          });
          throw new HttpError(404, `Production quantity not found for task ${qualityControl.task_id} and product ${qualityControl.item_id}. Cannot proceed with quality control approval.`);
        }

        // Store the original finished quantity before updating
        const originalFinishedQuantity = productionQuantity.finished_quantity || 0;

        // Create stock entry for rejected items if only infected_type is InfectedType.BacterialSevere or InfectedType.Fungal
        if (qualityControl.infected_items > 0 && (qualityControl.infected_type === InfectedType.BacterialSevere || qualityControl.infected_type === InfectedType.Fungal)) {

          try {
            const stockEntryResult = await this.createStockEntry(user, {
              purpose: STOCK_ENTRY_PURPOSE_MATERIAL_ISSUE,
              items: [{
                item_code: qualityControl.item_id || '',
                qty: qualityControl.infected_items,
                uom: qualityControl.uom_id || '',
                conversion_factor: DEFAULT_CONVERSION_FACTOR,
                s_warehouse: approveData.s_warehouse,
                t_warehouse: ''
              }],
              description: `Hủy sản phẩm không đạt chất lượng từ công việc QC \"${qualityControl.task?.label || qualityControl.task_id}\" (Kiểm tra ngày ${moment(qualityControl.inspection_date).format('DD/MM/YYYY')}${qualityControl.infected_type ? `, Loại nhiễm: ${qualityControl.infected_type}` : ''})`,
              iot_crop: cropInfo.crop_id,
              iot_farming_plan_task: qualityControl.task_id
            });
          }
          catch (error: any) {
            const detailedMessage = `Stock entry failed for quality control ${qualityControl.name} (task: ${qualityControl.task_id}, item: ${qualityControl.item_id}, qty: ${qualityControl.infected_items}, warehouse: ${approveData.s_warehouse}): ${error.message || 'Unknown error'}`;
            Logger.error('Stock entry for infected items failed', {
              qualityControlId: qualityControl.name,
              taskId: qualityControl.task_id,
              itemId: qualityControl.item_id,
              quantity: qualityControl.infected_items,
              warehouse: approveData.s_warehouse,
              error: error.message
            });
            throw new Error(detailedMessage);
          }

          // Validate that we have enough finished quantity to dispose
          if (originalFinishedQuantity < qualityControl.infected_items) {
            throw new HttpError(400, `Insufficient finished quantity (${originalFinishedQuantity}) to dispose infected items (${qualityControl.infected_items})`);
          }

          // Update production quantity for the current task
          // productionQuantity.quantity = qualityControl.quality_passed_items;
          productionQuantity.issued_quantity = (productionQuantity.issued_quantity || 0) + qualityControl.infected_items;

          // Calculate the new finished quantity (original minus infected items)
          const newFinishedQuantity = originalFinishedQuantity - qualityControl.infected_items;
          productionQuantity.finished_quantity = newFinishedQuantity;

          Logger.debug('Updating production quantity after quality control', {
            task_id: qualityControl.task_id,
            product_id: qualityControl.item_id,
            originalFinished: originalFinishedQuantity,
            newFinished: newFinishedQuantity,
            infected: qualityControl.infected_items,
            // passed: qualityControl.quality_passed_items
          });

          await productionQuantityRepo.save(productionQuantity);

          // Update total_qty_in_crop for all related tasks
          try {
            // Get all production quantities with the same product in the same crop
            const relatedProductionQuantities = await ERPExecute(`
              SELECT pq.name, pq.task_id, pq.quantity, pq.finished_quantity, pq.total_qty_in_crop
              FROM tabiot_production_quantity AS pq
              JOIN tabiot_farming_plan_task AS task ON pq.task_id = task.name
              JOIN tabiot_farming_plan_state AS state ON task.farming_plan_state = state.name
              JOIN tabiot_farming_plan AS plan ON state.farming_plan = plan.name
              WHERE plan.crop = $1
              AND pq.product_id = $2
              AND pq.deleted IS NULL
            `, [cropInfo.crop_id, qualityControl.item_id || '']);

            if (relatedProductionQuantities?.length > 0) {
              Logger.debug('Found related production quantities to update', {
                count: relatedProductionQuantities.length,
                productId: qualityControl.item_id,
                cropId: cropInfo.crop_id,
                discard: qualityControl.infected_items
              });

              // Calculate the change in total crop quantity (negative because we're discarding)
              const cropQuantityChange = -qualityControl.infected_items;

              // Update all related production quantities
              for (const relatedPQ of relatedProductionQuantities) {
                // Update total_qty_in_crop for each related production quantity
                const currentTotal = relatedPQ.total_qty_in_crop || 0;
                const newTotal = currentTotal + cropQuantityChange;

                // Validate that the new total is not negative (data consistency check)
                if (newTotal < 0) {
                  Logger.warn('Total quantity in crop would become negative, setting to 0', {
                    pqName: relatedPQ.name,
                    taskId: relatedPQ.task_id,
                    currentTotal,
                    cropQuantityChange,
                    calculatedTotal: newTotal
                  });
                }

                const finalTotal = Math.max(0, newTotal);

                await productionQuantityRepo.createQueryBuilder()
                  .update(ProductionQuantity)
                  .set({
                    total_qty_in_crop: finalTotal,
                    modified: new Date(),
                    modified_by: user.user_id
                  })
                  .where("name = :name", { name: relatedPQ.name })
                  .execute();

                Logger.debug('Updated total_qty_in_crop for related production quantity', {
                  pqName: relatedPQ.name,
                  taskId: relatedPQ.task_id,
                  oldTotal: currentTotal,
                  newTotal: finalTotal,
                  wasNegative: newTotal < 0
                });
              }
            }
          } catch (updateError) {
            Logger.error('Error updating related task production quantities:', updateError);
            throw updateError;
          }
        }

        // Update quality control status
        qualityControl.status = QC_STATUS_APPROVED;
        if (approveData.description) {
          qualityControl.description = approveData.description;
        }

        // Add a note to the description based on whether stock entries were processed
        if (qualityControl.infected_items > 0) {
          if (qualityControl.infected_type === InfectedType.BacterialSevere || qualityControl.infected_type === InfectedType.Fungal) {
            // Add a note that stock entries were processed
            qualityControl.description = `${qualityControl.description || ''}\nĐã xuất huỷ sản phẩm không đạt chất lượng: ${moment().format('DD/MM/YYYY HH:mm:ss')}`;
          } else {
            // Add a note that no stock entry was processed due to infected type
            // Translate infected type to Vietnamese
            let infectedTypeVietnamese = qualityControl.infected_type === InfectedType.BacterialLight ? 'nhiễm khuẩn nhẹ' : qualityControl.infected_type;

            qualityControl.description = `${qualityControl.description || ''}\nKhông thực hiện giao dịch kho do loại nhiễm ${infectedTypeVietnamese} không thuộc diện nhiễm khuẩn nặng hoặc nhiễm nấm: ${moment().format('DD/MM/YYYY HH:mm:ss')}`;
          }
        }

        await qualityControlRepo.save(qualityControl);

        // Also update the task to indicate quality control has been performed
        try {
          const taskRepository = AppDataSource.getRepository(FarmingPlanTask);
          await taskRepository.createQueryBuilder()
            .update(FarmingPlanTask)
            .set({
              status: TaskStatus.DONE,
              modified: new Date(),
              modified_by: user.user_id
            })
            .where("name = :taskId", { taskId: qualityControl.task_id })
            .execute();
        } catch (updateError) {
          Logger.error('Error updating task status after quality control:', updateError);
          throw updateError; // Now throw the error to trigger rollback
        }

        // Commit transaction
        await queryRunner.commitTransaction();

        return qualityControl;
      } catch (error) {
        // Rollback transaction on error
        await queryRunner.rollbackTransaction();
        Logger.error('Error approving quality control:', error);
        throw error;
      } finally {
        // Release query runner
        await queryRunner.release();
      }
    } catch (error) {
      Logger.error('Error approving quality control:', error);
      throw error;
    }
  }

  /**
   * Approves multiple quality control records in batch
   * Processes each item individually and provides detailed success/failure reporting
   *
   * @param user Current user
   * @param batchData Batch approval data with multiple quality control items
   * @returns Batch approval response with individual results
   */
  async batchApproveQualityControl(
    user: ICurrentUser,
    batchData: BatchApproveQualityControlDto
  ): Promise<BatchApprovalResponseDto> {
    try {
      Logger.info('Starting batch quality control approval', {
        userId: user.user_id,
        itemCount: batchData.items.length
      });

      const results: BatchApprovalResultItemDto[] = [];
      let successfulItems = 0;
      let failedItems = 0;

      // Process each quality control item individually
      for (const item of batchData.items) {
        try {
          // Get the existing quality control record to determine defaults
          const existingQualityControl = await this.getQualityControl(user, item.quality_control_id);
          if (!existingQualityControl) {
            throw new Error(`Quality control ${item.quality_control_id} not found or access denied`);
          }

          // Create individual approval DTO from batch item with fallback to existing values
          const approveData: ApproveQualityControlDto = {
            quality_control_id: item.quality_control_id,
            // Use provided values or fall back to existing values
            total_items: item.total_items !== undefined ? item.total_items : existingQualityControl.total_items,
            infected_items: item.infected_items !== undefined ? item.infected_items : existingQualityControl.infected_items,
            quality_passed_items: item.quality_passed_items !== undefined ? item.quality_passed_items : existingQualityControl.quality_passed_items,
            infected_type: item.infected_type !== undefined ? item.infected_type : existingQualityControl.infected_type,
            description: item.description || batchData.global_description,
            s_warehouse: item.s_warehouse,
            t_warehouse: item.t_warehouse
          };

          // Call the existing single approval method
          const approvedQualityControl = await this.approveQualityControl(user, approveData);

          // Record success
          results.push({
            quality_control_id: item.quality_control_id,
            status: 'success',
            message: 'Quality control approved successfully',
            data: approvedQualityControl
          });
          successfulItems++;

          Logger.debug('Successfully approved quality control in batch', {
            qualityControlId: item.quality_control_id,
            taskId: approvedQualityControl.task_id
          });

        } catch (error: any) {
          // Record failure
          const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
          results.push({
            quality_control_id: item.quality_control_id,
            status: 'error',
            message: errorMessage
          });
          failedItems++;

          Logger.error('Failed to approve quality control in batch', {
            qualityControlId: item.quality_control_id,
            error: errorMessage
          });
        }
      }

      const response: BatchApprovalResponseDto = {
        total_items: batchData.items.length,
        successful_items: successfulItems,
        failed_items: failedItems,
        results: results,
        message: `Batch approval completed: ${successfulItems} successful, ${failedItems} failed`
      };

      Logger.info('Completed batch quality control approval', {
        userId: user.user_id,
        totalItems: batchData.items.length,
        successfulItems,
        failedItems
      });

      return response;

    } catch (error) {
      Logger.error('Error in batch quality control approval:', error);
      throw error;
    }
  }

  /**
   * List all quality controls for the current user's customer with detailed information
   * @param user Current user information
   * @param page Page number for pagination
   * @param size Page size for pagination
   * @param filters Optional filters to apply to the query
   * @returns A paginated list of quality controls with detailed information
   */
  async listQualityControls(
    user: ICurrentUser,
    page: number = 1,
    size: number = 10,
    filters?: FilterTuple[]
  ): Promise<{ data: FarmingPlanTaskQualityControl[]; page: number; size: number; total: number }> {
    try {
      Logger.info('Starting listQualityControls', { userId: user.user_id, customerId: user.customer_id });

      const skip = (page - 1) * size;
      const take = size;

      // Get repository and create query builder
      const qualityControlRepo = AppDataSource.getRepository(FarmingPlanTaskQualityControl);

      // Create query builder with all necessary joins
      let qb = qualityControlRepo.createQueryBuilder('qc')
        .leftJoinAndSelect('qc.task', 'task')
        .leftJoinAndSelect('qc.item', 'item')
        .leftJoinAndSelect('qc.uom', 'uom')
        .leftJoinAndSelect('qc.inspectedByUser', 'inspectedByUser')
        .leftJoinAndSelect('qc.approveByUser', 'approvedByUser')
        .leftJoinAndSelect('task.assignedUser', 'assignedUser')
        .where('qc.customer_id = :customer_id', { customer_id: user.customer_id });

      // Apply dynamic filters if provided
      qb = applyQueryFilters(qb, filters, 'qc');
      // Add pagination and sorting
      qb = qb.orderBy('qc.creation', 'DESC').skip(skip).take(take);

      // Get data and count in a single query
      const [data, total] = await qb.getManyAndCount();

      Logger.info('Successfully retrieved quality controls', { count: data.length, total });

      return {
        data,
        page,
        size,
        total,
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      Logger.error('Error listing quality controls:', error);
      throw new HttpError(500, `Error listing quality controls: ${errorMessage}`);
    }
  }

  /**
   * Soft delete a quality control record
   * @param user Current user information
   * @param qualityControlId ID of the quality control to soft delete
   * @returns The soft deleted quality control record
   */
  async softDeleteQualityControl(
    user: ICurrentUser,
    qualityControlId: string
  ): Promise<FarmingPlanTaskQualityControl> {
    try {
      Logger.info('Starting softDeleteQualityControl', { userId: user.user_id, qualityControlId });

      // Get the quality control record
      const qualityControlRepo = AppDataSource.getRepository(FarmingPlanTaskQualityControl);
      const qualityControl = await qualityControlRepo.findOne({
        where: {
          name: qualityControlId,
          customer_id: user.customer_id
        }
      });

      if (!qualityControl) {
        throw new HttpError(404, 'Quality control record not found');
      }

      // Check if the quality control is already approved
      if (qualityControl.status === QC_STATUS_APPROVED) {
        throw new HttpError(400, 'Không thể xoá bản ghi đã được duyệt');
      }

      // Set the deleted flag (soft delete) using TypeORM's soft delete functionality
      qualityControl.deleted = new Date();
      qualityControl.modified = new Date();
      qualityControl.modified_by = user.user_id;

      // Save the updated record
      const result = await qualityControlRepo.save(qualityControl);
      Logger.info('Successfully soft deleted quality control', { qualityControlId });

      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      Logger.error('Error soft deleting quality control:', error);
      throw error instanceof HttpError ? error : new HttpError(500, `Error soft deleting quality control: ${errorMessage}`);
    }
  }
}
