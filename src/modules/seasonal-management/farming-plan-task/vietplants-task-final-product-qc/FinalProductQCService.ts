import Container, { Service } from 'typedi';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import moment from 'moment';
import { HttpError } from 'routing-controllers';

import { ICurrentUser } from '@app/interfaces';
import Logger from '@app/loaders/logger';
import { FilterTuple, applyQueryFilters } from '@app/utils/helpers/queryHelpter';
import { ERPExecute } from '@app/loaders/pglib/PGDB';
import { AppDataSource } from '@app/orm/dataSource';
import { FarmingPlanTask, TaskStatus } from '@app/orm/entities/farmingPlan/FarmingPlanTask';
import { ProductionQuantity } from '@app/orm/entities/farmingPlan/taskItem/ProductionQuantity';
import { FarmingPlanTaskFinalProductQC, FinalProductTransferType } from '@app/orm/entities/farmingPlan/taskItem/FarmingPlanTaskFinalProductQC';
import { StockEntryService } from '@app/modules/stock-v3/stock-entry/StockEntryService';
import {
  ApproveFinalProductQualityControlDto,
  SubmitFinalProductQualityControlDto,
  BatchSubmitFinalProductQualityControlDto,
  FinalProductQualityControlItemDto,
  FinalProductItemDto,
  BatchApproveFinalProductQualityControlDto,
  BatchApprovalFinalProductResponseDto,
  BatchApprovalFinalProductResultItemDto
} from './FinalProductQC.dto';

@Service()
export class FinalProductQCService {
  private finalProductQCRepo: Repository<FarmingPlanTaskFinalProductQC>;
  private taskRepo: Repository<FarmingPlanTask>;
  private stockEntryService: StockEntryService;

  constructor() {
    this.finalProductQCRepo = AppDataSource.getRepository(FarmingPlanTaskFinalProductQC);
    this.taskRepo = AppDataSource.getRepository(FarmingPlanTask);
    this.stockEntryService = Container.get(StockEntryService);
  }

  /**
   * Utility function to extract prefix from item label
   * @param itemLabel Item label (e.g., "BANANA_UNI_CL07")
   * @returns Prefix (e.g., "BANANA")
   */
  private extractItemPrefix(itemLabel: string): string {
    //GET the first word before the first underscore
    return itemLabel.split('_')[0];
  }




  /**
   * Validates quantity values for final product quality control
   * @param qualityItem Quality control item data
   */
  private validateTransferQuantities(qualityItem: FinalProductQualityControlItemDto): void {
    // Check for negative quantities
    if (qualityItem.transfer_items < 0) {
      throw new HttpError(400, 'Transfer items quantity cannot be negative');
    }

    if (qualityItem.total_items < 0) {
      throw new HttpError(400, 'Total items quantity cannot be negative');
    }

    // Validate that transfer_items doesn't exceed total_items
    if (qualityItem.transfer_items > qualityItem.total_items) {
      throw new HttpError(400, 'Transfer items cannot exceed total items');
    }
  }



  /**
   * Validates that sufficient stock is available in the source warehouse
   * @param user Current user
   * @param itemCode Item code to check
   * @param warehouse Warehouse to check
   * @param requiredQty Required quantity
   */
  private async validateStockAvailability(
    user: ICurrentUser,
    itemCode: string,
    warehouse: string,
    requiredQty: number
  ): Promise<void> {
    try {
      // Query current stock balance for the item in the warehouse
      const stockBalance = await ERPExecute(`
        SELECT
          COALESCE(SUM(actual_qty), 0) as available_qty
        FROM "tabBin"
        WHERE item_code = $1
        AND warehouse = $2
      `, [itemCode, warehouse]);

      const availableQty = stockBalance?.[0]?.available_qty || 0;

      if (availableQty < requiredQty) {
        throw new HttpError(400, `Insufficient stock in warehouse ${warehouse}. Available: ${availableQty}, Required: ${requiredQty} for item ${itemCode}`);
      }

      Logger.debug('Stock availability validated', {
        itemCode,
        warehouse,
        availableQty,
        requiredQty
      });
    } catch (error) {
      Logger.error('Error validating stock availability:', error);
      throw error;
    }
  }

  /**
   * Validates that warehouses exist and user has access to them
   * @param user Current user
   * @param sourceWarehouse Source warehouse
   * @param targetWarehouse Target warehouse
   */
  private async validateWarehouses(
    user: ICurrentUser,
    sourceWarehouse: string,
    targetWarehouse: string
  ): Promise<void> {
    try {
      if (!sourceWarehouse || !targetWarehouse) {
        throw new HttpError(400, 'Both source and target warehouses are required');
      }

      // Validate source warehouse exists and user has access
      const sourceWarehouseQuery = await ERPExecute(`
        SELECT name, warehouse_name
        FROM "tabWarehouse"
        WHERE name = $1 AND iot_customer = $2 AND is_deleted = 0
      `, [sourceWarehouse, user.customer_id]);

      if (!sourceWarehouseQuery || sourceWarehouseQuery.length === 0) {
        throw new HttpError(404, `Source warehouse ${sourceWarehouse} not found or access denied`);
      }

      // Validate target warehouse exists and user has access
      const targetWarehouseQuery = await ERPExecute(`
        SELECT name, warehouse_name
        FROM "tabWarehouse"
        WHERE name = $1 AND iot_customer = $2 AND is_deleted = 0
      `, [targetWarehouse, user.customer_id]);

      if (!targetWarehouseQuery || targetWarehouseQuery.length === 0) {
        throw new HttpError(404, `Target warehouse ${targetWarehouse} not found or access denied`);
      }

      Logger.debug('Warehouses validated successfully', {
        sourceWarehouse,
        targetWarehouse
      });
    } catch (error) {
      Logger.error('Error validating warehouses:', error);
      throw error;
    }
  }

  /**
     * Updates a task's status to In Progress
     *
     * @param user Current user
     * @param taskId Task ID to update
     * @returns Updated task
     */
  async updateTaskStatusToInProgress(
    user: ICurrentUser,
    taskId: string
  ): Promise<FarmingPlanTask> {
    try {
      // Validate task exists and user has access
      const taskValidation = await this.validateTaskAccess(user, taskId);
      if (!taskValidation) {
        throw new HttpError(404, `Task ${taskId} not found or access denied`);
      }

      // Get the actual task entity from the repository
      const task = await this.taskRepo.findOne({
        where: { name: taskId }
      });

      if (!task) {
        throw new HttpError(404, `Task ${taskId} not found`);
      }

      // Update task status to In Progress
      task.status = TaskStatus.IN_PROGRESS; // Using correct enum value
      task.modified = new Date();
      task.modified_by = user.user_id;

      await this.taskRepo.save(task);

      return task;
    } catch (error) {
      Logger.error('Error updating task status:', error);
      throw error;
    }
  }

  /**
   * Creates or updates production quantity for target item (BANANA_TP/XV)
   * @param user Current user
   * @param taskId Task ID
   * @param targetItemId Target item ID
   * @param quantity Quantity to add
   * @param uomId UOM ID
   */
  private async createOrUpdateTargetProductionQuantity(
    user: ICurrentUser,
    taskId: string,
    targetItemId: string,
    quantity: number,
    uomId: string
  ): Promise<void> {
    try {
      const productionQuantityRepo = AppDataSource.getRepository(ProductionQuantity);

      // Check if production quantity already exists for target item
      let targetProductionQuantity = await productionQuantityRepo.findOne({
        where: {
          task_id: taskId,
          product_id: targetItemId
        }
      });

      if (targetProductionQuantity) {
        Logger.debug('Target production quantity found, updating', {
          taskId,
          targetItemId,
          quantity,
          existingQuantity: targetProductionQuantity.quantity
        });

        // Update existing production quantity
        targetProductionQuantity.finished_quantity = (targetProductionQuantity.finished_quantity || 0) + quantity;
        targetProductionQuantity.quantity = (targetProductionQuantity.quantity || 0) + quantity;
        targetProductionQuantity.modified = new Date();
        targetProductionQuantity.modified_by = user.user_id;
      } else {
        Logger.debug('Target production quantity not found, creating', {
          taskId,
          targetItemId,
          quantity
        });

        // Create new production quantity for target item
        targetProductionQuantity = new ProductionQuantity();
        targetProductionQuantity.name = uuidv4();
        targetProductionQuantity.task_id = taskId;
        targetProductionQuantity.product_id = targetItemId;
        targetProductionQuantity.quantity = quantity;
        targetProductionQuantity.finished_quantity = quantity;
        targetProductionQuantity.issued_quantity = 0;
        targetProductionQuantity.lost_quantity = 0;
        targetProductionQuantity.draft_quantity = 0;
        targetProductionQuantity.total_qty_in_crop = 0; // Will be updated by crop quantity logic
        targetProductionQuantity.active_uom = uomId;
        targetProductionQuantity.active_conversion_factor = 1;
        targetProductionQuantity.creation = new Date();
        targetProductionQuantity.modified = new Date();
        targetProductionQuantity.owner = user.user_id.toString();
        targetProductionQuantity.modified_by = user.user_id;
      }

      await productionQuantityRepo.save(targetProductionQuantity);

      Logger.debug('Target production quantity created/updated', {
        taskId,
        targetItemId,
        quantity,
        isNew: !targetProductionQuantity.creation
      });
    } catch (error) {
      Logger.error('Error creating/updating target production quantity:', error);
      throw error;
    }
  }

  /**
   * Validates that a task exists and the user has access to it
   *
   * @param user Current user
   * @param taskId Task ID to validate
   * @returns Task object if valid, null otherwise
   */
  async validateTaskAccess(
    user: ICurrentUser,
    taskId: string
  ): Promise<{ name: string; label: string; status: any } | null> {
    try {
      const task = await ERPExecute(`
            SELECT task.name, task.label, task.status
            FROM tabiot_farming_plan_task AS task
            LEFT JOIN tabiot_farming_plan_state AS state ON task.farming_plan_state = state.name
            LEFT JOIN tabiot_farming_plan AS plan ON state.farming_plan = plan.name
            LEFT JOIN tabiot_crop AS crop ON plan.crop = crop.name
            LEFT JOIN tabiot_zone AS zone ON crop.zone_id = zone.name
            LEFT JOIN tabiot_customer AS customer ON zone.customer_id = customer.name

            WHERE task.name = $1 AND customer.name = $2
          `,
        [taskId, user.customer_id.toString() || '']
      );

      return task?.length > 0 ? task[0] : null;
    } catch (error) {
      Logger.error('Error validating task access:', error);
      return null;
    }
  }

  /**
   * Gets crop information for a task
   *
   * @param _user Current user (unused but kept for consistency)
   * @param taskId Task ID
   * @returns Crop information
   */
  async getCropInfoForTask(_user: ICurrentUser, taskId: string): Promise<{ crop_id: string; crop_name: string } | null> {
    try {
      const cropInfo = await ERPExecute(
        `
          SELECT 
            crop.name AS crop_id,
            crop.label AS crop_name
          FROM tabiot_farming_plan_task AS task
          LEFT JOIN tabiot_farming_plan_state AS state ON task.farming_plan_state = state.name
          LEFT JOIN tabiot_farming_plan AS plan ON state.farming_plan = plan.name
          LEFT JOIN tabiot_crop AS crop ON plan.crop = crop.name
          WHERE task.name = $1
        `,
        [taskId]
      );

      // Type checking for crop info
      if (cropInfo?.length > 0) {
        const result = cropInfo[0] as any;
        return {
          crop_id: result.crop_id,
          crop_name: result.crop_name
        };
      }

      return null;
    } catch (error) {
      Logger.error('Error getting crop info for task:', error);
      return null;
    }
  }

  /**
   * Gets the first production quantity for a task
   * Used to retrieve source item information
   * 
   * @param taskId Task ID to get production quantity for
   * @returns First production quantity for the task or null if none exists
   */
  async getFirstProductionQuantity(taskId: string): Promise<ProductionQuantity | null> {
    try {
      const productionRepo = AppDataSource.getRepository(ProductionQuantity);
      const productionQuantity = await productionRepo.findOne({
        where: { task_id: taskId },
        relations: ['product']
      });
      return productionQuantity;
    } catch (error) {
      Logger.error('Error getting production quantity:', error);
      return null;
    }
  }

  /**
   * Validates source and target items for final product transfer
   * Source item is the item at the and subculture stages
   * Target item with field item_name must have the same prefix as source item
   *
   * @param user Current user
   * @param sourceItemId Source item ID (should end with _CL07)
   * @param transferType Transfer type (TP for sale or XV for garden planting)
   * @returns Object with source and target item information
   */
  async validateAndGetTransferItems(
    user: ICurrentUser,
    sourceItemId: string,
    transferType: FinalProductTransferType
  ): Promise<{ sourceItem: FinalProductItemDto; targetItem: FinalProductItemDto } | null> {
    try {
      // Get source item
      const sourceItemQuery = await ERPExecute(
        `SELECT 
          name, 
          item_code, 
          item_name, 
          label 
        FROM "tabItem" 
        WHERE 
          name = $1 AND iot_customer = $2 AND is_deleted = 0`,
        [sourceItemId, user.customer_id]
      );

      if (!sourceItemQuery || sourceItemQuery.length === 0) {
        throw new HttpError(404, `Source item ${sourceItemId} not found`);
      }

      const sourceItem = sourceItemQuery[0];

      // Extract prefix from source item
      const sourcePrefix = this.extractItemPrefix(sourceItem.label);

      // Build expected target item label using utility function
      const targetItemQuery = await ERPExecute(
        `SELECT
          name,
          item_code,
          item_name,
          label
        FROM "tabItem"
        WHERE
          label LIKE $1
          AND label LIKE $2
          AND iot_customer = $3
          AND is_deleted = 0
        LIMIT 1`,
        [`${sourcePrefix}%`, `%_${transferType}`, user.customer_id]
      );

      if (!targetItemQuery || targetItemQuery.length === 0) {
        const expectedSuffix = transferType === FinalProductTransferType.TP ? '_TP' : '_XV';
        throw new HttpError(404, `Target item not found. Expected target item with prefix "${sourcePrefix}" and suffix "${expectedSuffix}" for transfer type ${transferType}`);
      }

      const targetItem = targetItemQuery[0];

      Logger.info('Validated transfer items successfully', {
        sourceItem: sourceItem.item_code,
        targetItem: targetItem.item_code,
        prefix: sourcePrefix,
        transferType: transferType
      });

      return { sourceItem, targetItem };
    } catch (error) {
      Logger.error('Error validating transfer items:', error);
      throw error;
    }
  }

  /**
   * Updates total_qty_in_crop for all related production quantities in the same crop
   * Following the same pattern as the original quality control service
   *
   * @param user Current user
   * @param cropId Crop ID
   * @param productId Product ID (source item)
   * @param quantityChange Change in quantity (negative for disposal)
   */
  private async updateCropQuantities(
    user: ICurrentUser,
    cropId: string,
    productId: string,
    quantityChange: number
  ): Promise<void> {
    try {
      // Get all production quantities with the same product in the same crop
      const relatedProductionQuantities = await ERPExecute(`
        SELECT pq.name, pq.task_id, pq.quantity, pq.finished_quantity, pq.total_qty_in_crop
        FROM tabiot_production_quantity AS pq
        JOIN tabiot_farming_plan_task AS task ON pq.task_id = task.name
        JOIN tabiot_farming_plan_state AS state ON task.farming_plan_state = state.name
        JOIN tabiot_farming_plan AS plan ON state.farming_plan = plan.name
        WHERE plan.crop = $1
        AND pq.product_id = $2
        AND pq.deleted IS NULL
      `, [cropId, productId]);

      if (relatedProductionQuantities?.length > 0) {
        Logger.debug('Found related production quantities to update', {
          count: relatedProductionQuantities.length,
          productId: productId,
          cropId: cropId,
          quantityChange: quantityChange
        });

        const productionQuantityRepo = AppDataSource.getRepository(ProductionQuantity);

        // Update all related production quantities
        for (const relatedPQ of relatedProductionQuantities) {
          // Update total_qty_in_crop for each related production quantity
          const currentTotal = relatedPQ.total_qty_in_crop || 0;
          const newTotal = Math.max(0, currentTotal + quantityChange);

          await productionQuantityRepo.createQueryBuilder()
            .update(ProductionQuantity)
            .set({
              total_qty_in_crop: newTotal,
              modified: new Date(),
              modified_by: user.user_id
            })
            .where("name = :name", { name: relatedPQ.name })
            .execute();

          Logger.debug('Updated total_qty_in_crop for related production quantity', {
            pqName: relatedPQ.name,
            taskId: relatedPQ.task_id,
            oldTotal: currentTotal,
            newTotal: newTotal
          });
        }
      }
    } catch (error) {
      Logger.error('Error updating crop quantities:', error);
      throw error;
    }
  }

  /**
   * Creates dual stock entries for final product transfer
   * 1. Material Issue from source warehouse (dispose BANANA_CL07)
   * 2. Material Receipt to target warehouse (receive BANANA_TP/XV)
   *
   * @param user Current user
   * @param params Transfer parameters
   * @returns Results of both stock entries
   */
  async createTransferStockEntries(
    user: ICurrentUser,
    params: {
      sourceItem: FinalProductItemDto;
      targetItem: FinalProductItemDto;
      quantity: number;
      sourceWarehouse: string;
      targetWarehouse: string;
      description: string;
      cropId: string;
      taskId: string;
    }
  ): Promise<{ disposeResult: any; receiptResult: any }> {
    try {
      // 1. Create disposal stock entry (Material Issue for source item)
      const disposeStockEntry = {
        __islocal: 1,
        __unsaved: 1,
        posting_date: moment().format('YYYY-MM-DD'),
        posting_time: moment().format('HH:mm:ss'),
        set_posting_time: 1,
        doctype: 'Stock Entry',
        company: 'VIIS',
        purpose: 'Material Issue',
        stock_entry_type: 'Material Issue',
        iot_customer_user: user.user_id.toString(),
        iot_crop: params.cropId,
        iot_farming_plan_task: params.taskId,
        description: `${params.description} - Xuất hủy ${params.sourceItem.item_name}`,
        items: [{
          name: `row ${uuidv4()}`,
          item_code: params.sourceItem.item_code,
          qty: params.quantity,
          uom: params.sourceItem.stock_uom,
          conversion_factor: 1,
          doctype: 'Stock Entry Detail',
          s_warehouse: params.sourceWarehouse,
          t_warehouse: '',
        }]
      };

      // 2. Create receipt stock entry (Material Receipt for target item)
      const receiptStockEntry = {
        __islocal: 1,
        __unsaved: 1,
        posting_date: moment().format('YYYY-MM-DD'),
        posting_time: moment().format('HH:mm:ss'),
        set_posting_time: 1,
        doctype: 'Stock Entry',
        company: 'VIIS',
        purpose: 'Material Receipt',
        stock_entry_type: 'Material Receipt',
        iot_customer_user: user.user_id.toString(),
        iot_crop: params.cropId,
        iot_farming_plan_task: params.taskId,
        description: `${params.description} - Nhập ${params.targetItem.item_name}`,
        items: [{
          name: `row ${uuidv4()}`,
          item_code: params.targetItem.item_code,
          qty: params.quantity,
          uom: params.targetItem.stock_uom,
          conversion_factor: 1,
          basic_rate: params.targetItem.valuation_rate,
          doctype: 'Stock Entry Detail',
          s_warehouse: '',
          t_warehouse: params.targetWarehouse
        }]
      };

      // Execute both stock entries with proper error handling
      let disposeSubmitResult: any;
      let receiptSubmitResult: any;

      try {
        // 1. Create and submit disposal stock entry
        const disposeResult = await this.stockEntryService.saveStockEntryAdmin(user, disposeStockEntry);
        disposeSubmitResult = await this.stockEntryService.submitStockEntryAdmin(user, disposeResult.docs[0]);

        // 2. Create and submit receipt stock entry
        const receiptResult = await this.stockEntryService.saveStockEntryAdmin(user, receiptStockEntry);
        receiptSubmitResult = await this.stockEntryService.submitStockEntryAdmin(user, receiptResult.docs[0]);

        Logger.info('Both stock entries created successfully', {
          disposeEntry: disposeSubmitResult?.docs?.[0]?.name,
          receiptEntry: receiptSubmitResult?.docs?.[0]?.name
        });

        return {
          disposeResult: disposeSubmitResult,
          receiptResult: receiptSubmitResult
        };
      } catch (error) {
        Logger.error('Error in stock entry creation process:', {
          error: error,
          disposeCompleted: !!disposeSubmitResult,
          receiptCompleted: !!receiptSubmitResult
        });
        throw error;
      }
    } catch (error) {
      Logger.error('Error creating transfer stock entries:', error);
      throw error;
    }
  }

  /**
   * Submits a final product quality control check for a task
   * Records the inspection results in the farming_plan_task_final_product_qc table
   *
   * @param user Current user
   * @param qualityData Quality control data
   * @returns Created quality control record
   */
  async submitQualityControl(
    user: ICurrentUser,
    qualityData: SubmitFinalProductQualityControlDto
  ): Promise<FarmingPlanTaskFinalProductQC> {
    try {
      // Validate task exists and user has access
      const task = await this.validateTaskAccess(user, qualityData.task_id);
      if (!task) {
        throw new HttpError(404, `Task ${qualityData.task_id} not found or access denied`);
      }

      // Get first production quantity for this task to use for source item info
      const productionQuantity = await this.getFirstProductionQuantity(qualityData.task_id);
      if (!productionQuantity) {
        throw new HttpError(404, `No production quantity found for task ${qualityData.task_id}`);
      }

      // Find all existing quality control records for this task (exclude soft deleted)
      const allExistingControls = await this.finalProductQCRepo
        .createQueryBuilder('qc')
        .where('qc.task_id = :task_id', { task_id: qualityData.task_id })
        .andWhere('qc.deleted IS NULL')
        .getMany();

      // Filter out quality items that already have a matching record
      const itemsToCreate = qualityData.quality_items.filter(qualityItem => {
        // Check if there's an existing record with the same transfer_type
        const matchingRecord = allExistingControls.find(control =>
          control.transfer_type === qualityItem.transfer_type
        );

        // If no matching record was found, we should create this item
        return !matchingRecord;
      });

      Logger.info('Final product quality control filtering', {
        task_id: qualityData.task_id,
        totalItems: qualityData.quality_items.length,
        existingItems: allExistingControls.length,
        itemsToCreate: itemsToCreate.length
      });

      // If all items already exist, return the first existing record
      if (itemsToCreate.length === 0 && allExistingControls.length > 0) {
        Logger.info('All final product quality control items already exist for this task', {
          task_id: qualityData.task_id
        });

        // Return the first existing record
        return allExistingControls[0];
      }

      // Use transaction to prevent race conditions
      const submitQueryRunner = AppDataSource.createQueryRunner();
      await submitQueryRunner.connect();
      await submitQueryRunner.startTransaction();

      try {
        // Process each quality control item within transaction
        for (const item of itemsToCreate) {
          // Validate quantities first
          this.validateTransferQuantities(item);

          // Validate and get transfer items
          const transferItems = await this.validateAndGetTransferItems(
            user,
            item.source_item_id || productionQuantity.product_id || '',
            item.transfer_type
          );

          if (!transferItems) {
            throw new HttpError(400, `Invalid transfer items for ${item.transfer_type}`);
          }

          // Double-check for existing record within transaction to prevent race condition
          const existingRecord = await submitQueryRunner.manager
            .createQueryBuilder(FarmingPlanTaskFinalProductQC, 'qc')
            .where('qc.task_id = :task_id', { task_id: qualityData.task_id })
            .andWhere('qc.transfer_type = :transfer_type', { transfer_type: item.transfer_type })
            .andWhere('qc.deleted IS NULL')
            .getOne();

          if (existingRecord) {
            Logger.warn('Quality control record already exists, skipping creation', {
              task_id: qualityData.task_id,
              transfer_type: item.transfer_type
            });
            continue;
          }

          // Create quality control record
          const qualityControl = new FarmingPlanTaskFinalProductQC();
          qualityControl.name = uuidv4();
          qualityControl.task_id = qualityData.task_id;
          qualityControl.source_item_id = transferItems.sourceItem.name;
          qualityControl.target_item_id = transferItems.targetItem.name;
          qualityControl.total_items = item.total_items;
          qualityControl.transfer_items = item.transfer_items;
          qualityControl.transfer_type = item.transfer_type;
          qualityControl.inspection_date = qualityData.inspection_date ? new Date(qualityData.inspection_date) : new Date();
          qualityControl.inspected_by = user.user_id.toString();
          qualityControl.description = qualityData.description || '';
          qualityControl.status = 'Draft';
          qualityControl.customer_id = user.customer_id.toString();

          // Save quality control record within transaction
          await submitQueryRunner.manager.save(qualityControl);
        }

        // Commit transaction
        await submitQueryRunner.commitTransaction();
      } catch (error) {
        // Rollback transaction on error
        await submitQueryRunner.rollbackTransaction();
        Logger.error('Error in submit quality control transaction:', error);
        throw error;
      } finally {
        // Release query runner
        await submitQueryRunner.release();
      }

      // Get all quality control records for the task (both existing and newly created, exclude soft deleted)
      const qualityControls = await this.finalProductQCRepo
        .createQueryBuilder('qc')
        .leftJoinAndSelect('qc.sourceItem', 'sourceItem')
        .leftJoinAndSelect('qc.targetItem', 'targetItem')
        .where('qc.task_id = :task_id', { task_id: qualityData.task_id })
        .andWhere('qc.deleted IS NULL')
        .getMany();

      Logger.info('Completed submitQualityControl for final products', {
        task_id: qualityData.task_id,
        totalRecords: qualityControls.length,
        itemsCreated: itemsToCreate.length,
        itemsSkipped: qualityData.quality_items.length - itemsToCreate.length
      });

      // Return the first record
      return qualityControls[0];
    } catch (error) {
      Logger.error('Error submitting final product quality control:', error);
      throw error;
    }
  }

  /**
   * Approves a final product quality control record
   * Creates transfer stock entries (disposal + receipt) and updates production quantities
   *
   * @param user Current user
   * @param approveData Approval data
   * @returns Updated quality control record
   */
  async approveQualityControl(
    user: ICurrentUser,
    approveData: ApproveFinalProductQualityControlDto
  ): Promise<FarmingPlanTaskFinalProductQC> {
    try {
      // Get quality control record
      const qualityControl = await this.getQualityControl(user, approveData.quality_control_id);
      if (!qualityControl) {
        throw new HttpError(404, `Quality control ${approveData.quality_control_id} not found or access denied`);
      }

      // Validate quality control is in Draft status
      if (qualityControl.status !== 'Draft') {
        Logger.info('Final product quality control is already approved, cannot edit or approve again', {
          quality_control_id: approveData.quality_control_id,
          status: qualityControl.status
        });

        throw new HttpError(400, `Cannot approve quality control ${approveData.quality_control_id} because it's already in ${qualityControl.status} status`);
      }

      // Get repositories
      const productionQuantityRepo = AppDataSource.getRepository(ProductionQuantity);

      // Update quantity fields if provided in approveData
      if (approveData.total_items !== undefined) {
        qualityControl.total_items = approveData.total_items;
      }

      if (approveData.transfer_items !== undefined) {
        qualityControl.transfer_items = approveData.transfer_items;
      }



      // Update transfer_type and infected_type if provided
      if (approveData.transfer_type !== undefined) {
        qualityControl.transfer_type = approveData.transfer_type;
      }

      // Validate quantities after updates
      const tempQualityItem = {
        total_items: qualityControl.total_items,
        transfer_items: qualityControl.transfer_items,
        transfer_type: qualityControl.transfer_type
      } as FinalProductQualityControlItemDto;

      this.validateTransferQuantities(tempQualityItem);

      // Validate warehouses (required for final product transfers)
      await this.validateWarehouses(user, approveData.s_warehouse, approveData.t_warehouse);

      // Record the approver
      qualityControl.approve_by = user.user_id.toString();

      // Get crop info for stock entry
      const cropInfo = await this.getCropInfoForTask(user, qualityControl.task_id);
      if (!cropInfo) {
        throw new HttpError(404, `Could not determine crop for task ${qualityControl.task_id}`);
      }

      // Start transaction
      const queryRunner = AppDataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Find the existing production quantity for source item
        const sourceProductionQuantity = await productionQuantityRepo.findOne({
          where: {
            task_id: qualityControl.task_id,
            product_id: qualityControl.source_item_id || ''
          }
        });

        if (!sourceProductionQuantity) {
          Logger.error('Source production quantity not found for task and product', {
            task_id: qualityControl.task_id,
            product_id: qualityControl.source_item_id
          });
          throw new HttpError(404, `Source production quantity not found for task ${qualityControl.task_id} and product ${qualityControl.source_item_id}`);
        }

        // Create transfer stock entries if transfer_items > 0 (Final Product QC always processes transfers)
        if (qualityControl.transfer_items > 0) {
          // Validate stock availability before creating transfers
          await this.validateStockAvailability(
            user,
            qualityControl.sourceItem?.item_code || '',
            approveData.s_warehouse,
            qualityControl.transfer_items
          );

          try {
            const transferResult = await this.createTransferStockEntries(user, {
              sourceItem: qualityControl.sourceItem as FinalProductItemDto,
              targetItem: qualityControl.targetItem as FinalProductItemDto,
              quantity: qualityControl.transfer_items,
              sourceWarehouse: approveData.s_warehouse,
              targetWarehouse: approveData.t_warehouse,
              description: `Chuyển đổi thành phẩm từ công việc QC "${qualityControl.task?.label || qualityControl.task_id}" (Kiểm tra ngày ${moment(qualityControl.inspection_date).format('DD/MM/YYYY')})`,
              cropId: cropInfo.crop_id,
              taskId: qualityControl.task_id
            });

            Logger.info('Transfer stock entries created successfully', {
              disposeEntry: transferResult.disposeResult?.docs?.[0]?.name,
              receiptEntry: transferResult.receiptResult?.docs?.[0]?.name
            });
          } catch (error) {
            Logger.error('Transfer stock entries failed', {
              error: error,
              sourceItem: qualityControl.sourceItem?.item_code,
              targetItem: qualityControl.targetItem?.item_code,
              quantity: qualityControl.transfer_items,
              sourceWarehouse: approveData.s_warehouse,
              targetWarehouse: approveData.t_warehouse
            });
            throw error; // Preserve original error type
          }

          // Update source production quantity
          const originalFinishedQuantity = sourceProductionQuantity.finished_quantity || 0;
          const newFinishedQuantity = Math.max(0, originalFinishedQuantity - qualityControl.transfer_items);
          sourceProductionQuantity.finished_quantity = newFinishedQuantity;
          sourceProductionQuantity.issued_quantity = (sourceProductionQuantity.issued_quantity || 0) + qualityControl.transfer_items;

          Logger.debug('Updating source production quantity after final product QC', {
            task_id: qualityControl.task_id,
            source_product_id: qualityControl.source_item_id,
            originalFinished: originalFinishedQuantity,
            newFinished: newFinishedQuantity,
            transferred: qualityControl.transfer_items
          });

          await productionQuantityRepo.save(sourceProductionQuantity);

          // Create or update production quantity for target item
          await this.createOrUpdateTargetProductionQuantity(
            user,
            qualityControl.task_id,
            qualityControl.target_item_id || '',
            qualityControl.transfer_items,
            qualityControl.targetItem?.stock_uom || ''
          );

          // Update total_qty_in_crop for all related production quantities in the same crop
          // Calculate the change in total crop quantity (negative because we're transferring out)
          const cropQuantityChange = -qualityControl.transfer_items;
          await this.updateCropQuantities(
            user,
            cropInfo.crop_id,
            qualityControl.source_item_id || '',
            cropQuantityChange
          );

          // Also update total_qty_in_crop for target item (positive because we're receiving)
          await this.updateCropQuantities(
            user,
            cropInfo.crop_id,
            qualityControl.target_item_id || '',
            qualityControl.transfer_items
          );
        }

        // Update quality control status
        qualityControl.status = 'Approved';
        if (approveData.description) {
          qualityControl.description = approveData.description;
        }

        // Add processing note to description for completed transfers
        if (qualityControl.transfer_items > 0) {
          // Add a note that stock entries were processed
          const transferTypeVietnamese = qualityControl.transfer_type === FinalProductTransferType.TP ? 'thành phẩm bán' : 'xuất vườn';
          qualityControl.description = `${qualityControl.description || ''}\nĐã thực hiện chuyển đổi sang ${transferTypeVietnamese}: ${moment().format('DD/MM/YYYY HH:mm:ss')}`;
        }

        await this.finalProductQCRepo.save(qualityControl);

        // Update task status to Done
        try {
          const taskRepository = AppDataSource.getRepository(FarmingPlanTask);
          await taskRepository.createQueryBuilder()
            .update(FarmingPlanTask)
            .set({
              status: TaskStatus.DONE,
              modified: new Date(),
              modified_by: user.user_id
            })
            .where("name = :taskId", { taskId: qualityControl.task_id })
            .execute();
        } catch (updateError) {
          Logger.error('Error updating task status after final product quality control:', updateError);
          throw updateError;
        }

        // Commit transaction
        await queryRunner.commitTransaction();

        return qualityControl;
      } catch (error) {
        // Rollback transaction on error
        await queryRunner.rollbackTransaction();
        Logger.error('Error approving final product quality control:', error);
        throw error;
      } finally {
        // Release query runner
        await queryRunner.release();
      }
    } catch (error) {
      Logger.error('Error approving final product quality control:', error);
      throw error;
    }
  }

  /**
   * Gets a final product quality control record by ID
   *
   * @param user Current user
   * @param qualityControlId Quality control ID
   * @returns Quality control record
   */
  async getQualityControl(
    user: ICurrentUser,
    qualityControlId: string
  ): Promise<FarmingPlanTaskFinalProductQC> {
    try {
      // Find quality control record
      const qualityControl = await this.finalProductQCRepo.findOne({
        where: {
          name: qualityControlId
        },
        relations: {
          sourceItem: true,
          targetItem: true,
          inspectedByUser: true,
          approveByUser: true,
          task: {
            assignedUser: true
          }
        }
      });

      // Validate quality control exists and user has access
      if (!qualityControl || qualityControl.customer_id !== user.customer_id?.toString()) {
        throw new HttpError(404, `Quality control ${qualityControlId} not found or access denied`);
      }

      return qualityControl;
    } catch (error) {
      Logger.error('Error getting final product quality control:', error);
      throw error;
    }
  }

  /**
   * Lists final product quality control records for a task
   *
   * @param user Current user
   * @param taskId Task ID
   * @returns List of quality control records
   */
  async listQualityControlsByTask(
    user: ICurrentUser,
    taskId: string
  ): Promise<FarmingPlanTaskFinalProductQC[]> {
    try {
      // Validate task exists and user has access
      const task = await this.validateTaskAccess(user, taskId);
      if (!task) {
        throw new HttpError(404, `Task ${taskId} not found or access denied`);
      }

      // Find quality control records (include soft delete filter)
      const qualityControls = await this.finalProductQCRepo
        .createQueryBuilder('qc')
        .leftJoinAndSelect('qc.sourceItem', 'sourceItem')
        .leftJoinAndSelect('qc.targetItem', 'targetItem')
        .leftJoinAndSelect('qc.uom', 'uom')
        .where('qc.task_id = :taskId', { taskId })
        .andWhere('qc.customer_id = :customerId', { customerId: user.customer_id?.toString() })
        .andWhere('qc.deleted IS NULL')
        .getMany();

      return qualityControls;
    } catch (error) {
      Logger.error('Error listing final product quality controls:', error);
      throw error;
    }
  }

  /**
   * List all final product quality controls for the current user's customer with detailed information
   * @param user Current user information
   * @param page Page number for pagination
   * @param size Page size for pagination
   * @param filters Optional filters to apply to the query
   * @returns A paginated list of quality controls with detailed information
   */
  async listQualityControls(
    user: ICurrentUser,
    page: number = 1,
    size: number = 10,
    filters?: FilterTuple[]
  ): Promise<{ data: FarmingPlanTaskFinalProductQC[]; page: number; size: number; total: number }> {
    try {
      Logger.info('Starting listQualityControls for final products', { userId: user.user_id, customerId: user.customer_id });

      const skip = (page - 1) * size;
      const take = size;

      // Create query builder with all necessary joins
      let qb = this.finalProductQCRepo.createQueryBuilder('qc')
        .leftJoinAndSelect('qc.task', 'task')
        .leftJoinAndSelect('qc.sourceItem', 'sourceItem')
        .leftJoinAndSelect('qc.targetItem', 'targetItem')
        .leftJoinAndSelect('qc.inspectedByUser', 'inspectedByUser')
        .leftJoinAndSelect('qc.approveByUser', 'approvedByUser')
        .leftJoinAndSelect('task.assignedUser', 'assignedUser')
        .where('qc.customer_id = :customer_id', { customer_id: user.customer_id?.toString() })
        .andWhere('qc.deleted IS NULL');

      // Apply dynamic filters if provided
      qb = applyQueryFilters(qb, filters, 'qc');

      // Add pagination and sorting
      qb = qb.orderBy('qc.creation', 'DESC').skip(skip).take(take);

      // Get data and count in a single query
      const [data, total] = await qb.getManyAndCount();

      Logger.info('Successfully retrieved final product quality controls', { count: data.length, total });

      return {
        data,
        page,
        size,
        total,
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      Logger.error('Error listing final product quality controls:', error);
      throw new HttpError(500, `Error listing final product quality controls: ${errorMessage}`);
    }
  }

  /**
   * Soft delete a final product quality control record
   * @param user Current user information
   * @param qualityControlId ID of the quality control to soft delete
   * @returns The soft deleted quality control record
   */
  async softDeleteQualityControl(
    user: ICurrentUser,
    qualityControlId: string
  ): Promise<FarmingPlanTaskFinalProductQC> {
    try {
      Logger.info('Starting softDeleteQualityControl for final product', { userId: user.user_id, qualityControlId });

      // Get the quality control record (include soft delete filter)
      const qualityControl = await this.finalProductQCRepo
        .createQueryBuilder('qc')
        .where('qc.name = :qualityControlId', { qualityControlId })
        .andWhere('qc.customer_id = :customerId', { customerId: user.customer_id?.toString() })
        .andWhere('qc.deleted IS NULL')
        .getOne();

      if (!qualityControl) {
        throw new HttpError(404, 'Final product quality control record not found');
      }

      // Check if the quality control is already approved
      if (qualityControl.status === 'Approved') {
        throw new HttpError(400, 'Không thể xoá bản ghi đã được duyệt');
      }

      // Set the deleted flag (soft delete)
      qualityControl.deleted = new Date();
      qualityControl.modified = new Date();
      qualityControl.modified_by = user.user_id;

      // Save the updated record
      const result = await this.finalProductQCRepo.save(qualityControl);
      Logger.info('Successfully soft deleted final product quality control', { qualityControlId });

      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      Logger.error('Error soft deleting final product quality control:', error);
      throw error instanceof HttpError ? error : new HttpError(500, `Error soft deleting final product quality control: ${errorMessage}`);
    }
  }

  /**
   * Submits batch final product quality control checks for multiple tasks
   * Updates current task status to In Progress and processes each quality check task
   *
   * @param user Current user
   * @param batchData Batch quality control data
   * @returns Results of batch quality control submission
   */
  async submitBatchQualityControl(
    user: ICurrentUser,
    batchData: BatchSubmitFinalProductQualityControlDto
  ): Promise<{
    currentTask: FarmingPlanTask;
    qualityControls: FarmingPlanTaskFinalProductQC[][];
  }> {
    try {
      Logger.info('Starting batch final product quality control submission', {
        current_task_id: batchData.current_task_id,
        quality_check_tasks_count: batchData.quality_check_tasks.length
      });

      // Update current task status to In Progress
      const currentTask = await this.updateTaskStatusToInProgress(user, batchData.current_task_id);

      // Track all created quality controls
      const allQualityControls: FarmingPlanTaskFinalProductQC[][] = [];

      // Process each quality check task
      for (const qualityTask of batchData.quality_check_tasks) {
        const qualityControl = await this.submitQualityControl(user, qualityTask);
        // submitQualityControl returns a single object, so wrap it in an array
        allQualityControls.push([qualityControl]);
      }

      Logger.info('Completed batch final product quality control submission', {
        current_task_id: batchData.current_task_id,
        tasks_processed: batchData.quality_check_tasks.length,
        controls_created: allQualityControls.flat().length
      });

      return {
        currentTask,
        qualityControls: allQualityControls
      };
    } catch (error) {
      Logger.error('Error submitting batch final product quality control:', error);
      throw error;
    }
  }

  /**
   * Approves multiple final product quality control records in batch
   * Processes each item individually and provides detailed success/failure reporting
   * Follows the same quantity field handling logic as the quality control module
   *
   * @param user Current user
   * @param batchData Batch approval data with multiple quality control items
   * @returns Batch approval response with individual results
   */
  async batchApproveFinalProductQualityControl(
    user: ICurrentUser,
    batchData: BatchApproveFinalProductQualityControlDto
  ): Promise<BatchApprovalFinalProductResponseDto> {
    try {
      Logger.info('Starting batch final product quality control approval', {
        userId: user.user_id,
        itemCount: batchData.items.length
      });

      const results: BatchApprovalFinalProductResultItemDto[] = [];
      let successfulItems = 0;
      let failedItems = 0;

      // Process each quality control item individually
      for (const item of batchData.items) {
        try {
          // Get the existing quality control record to determine defaults
          const existingQualityControl = await this.getQualityControl(user, item.quality_control_id);
          if (!existingQualityControl) {
            throw new Error(`Quality control ${item.quality_control_id} not found or access denied`);
          }

          // Create individual approval DTO from batch item with fallback to existing values
          const approveData: ApproveFinalProductQualityControlDto = {
            quality_control_id: item.quality_control_id,
            // Use provided values or fall back to existing values
            total_items: item.total_items !== undefined ? item.total_items : existingQualityControl.total_items,
            transfer_items: item.transfer_items !== undefined ? item.transfer_items : existingQualityControl.transfer_items,
            transfer_type: item.transfer_type !== undefined ? item.transfer_type : existingQualityControl.transfer_type,
            description: item.description || batchData.global_description,
            s_warehouse: item.s_warehouse || '',
            t_warehouse: item.t_warehouse || ''
          };

          // Validate required fields for approval
          if (!approveData.s_warehouse || !approveData.t_warehouse) {
            throw new Error('Source warehouse (s_warehouse) and target warehouse (t_warehouse) are required for final product approval');
          }

          // Call the existing single approval method
          const approvedQualityControl = await this.approveQualityControl(user, approveData);

          // Record success
          results.push({
            quality_control_id: item.quality_control_id,
            status: 'success',
            message: 'Final product quality control approved successfully',
            data: approvedQualityControl
          });
          successfulItems++;

          Logger.debug('Successfully approved final product quality control in batch', {
            qualityControlId: item.quality_control_id,
            taskId: approvedQualityControl.task_id
          });

        } catch (error: any) {
          console.log("error in catch", error);
          // Record failure
          const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
          results.push({
            quality_control_id: item.quality_control_id,
            status: 'error',
            message: errorMessage
          });
          failedItems++;

          Logger.error('Failed to approve final product quality control in batch', {
            qualityControlId: item.quality_control_id,
            error: errorMessage
          });
        }
      }

      const response: BatchApprovalFinalProductResponseDto = {
        total_items: batchData.items.length,
        successful_items: successfulItems,
        failed_items: failedItems,
        results: results,
        message: `Batch approval completed: ${successfulItems} successful, ${failedItems} failed`
      };

      Logger.info('Completed batch final product quality control approval', {
        userId: user.user_id,
        totalItems: batchData.items.length,
        successfulItems,
        failedItems
      });

      return response;

    } catch (error) {
      Logger.error('Error in batch final product quality control approval:', error);
      throw error;
    }
  }
}
